"""
Tests for the workflow schema converter.
"""

import json
import os
import sys

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
)

# Try to import pytest, but make it optional for direct execution
try:
    import pytest
    PYTEST_AVAILABLE = True
except ImportError:
    PYTEST_AVAILABLE = False
    # Mock pytest.main for direct execution
    class MockPytest:
        @staticmethod
        def main(args):
            print("pytest not available, running tests directly...")
            # Run all test functions
            test_functions = [
                test_loop_node_conversion  # Only run the LoopNode test for now
            ]
            for test_func in test_functions:
                try:
                    print(f"Running {test_func.__name__}...")
                    test_func()
                    print(f"✅ {test_func.__name__} PASSED")
                except Exception as e:
                    print(f"❌ {test_func.__name__} FAILED: {e}")
                    return 1
            print("🎉 All tests passed!")
            return 0
    pytest = MockPytest()


def test_approval_required_field():
    """
    Test that the approval_required field is correctly populated from the requires_approval field.
    """
    # Load the sample workflow
    with open(os.path.join("testing", "sample_workflow.json"), "r") as f:
        workflow_data = json.load(f)

    # Convert the workflow to transition schema
    transition_schema = convert_workflow_to_transition_schema(workflow_data)

    # Check that the transitions have the approval_required field
    for transition in transition_schema["transitions"]:
        node_id = transition["node_info"]["node_id"]
        transition_id = transition["id"].replace("transition-", "")

        # Find the corresponding node in the original workflow
        original_node = None

        # First, try to find the node by its ID matching the transition ID
        for node in workflow_data["nodes"]:
            if node["id"] == transition_id:
                original_node = node
                break

        # If we didn't find a node, try other methods
        if not original_node:
            for node in workflow_data["nodes"]:
                # Check if the node's server_id matches the node_id in the transition
                if (
                    "data" in node
                    and "definition" in node["data"]
                    and "mcp_info" in node["data"]["definition"]
                    and "server_id" in node["data"]["definition"]["mcp_info"]
                    and node["data"]["definition"]["mcp_info"]["server_id"] == node_id
                ):
                    original_node = node
                    break

                # Check if the node's definition name matches the node_id in the transition
                if (
                    "data" in node
                    and "definition" in node["data"]
                    and "name" in node["data"]["definition"]
                    and node["data"]["definition"]["name"] == node_id
                ):
                    original_node = node
                    break

        if original_node and "data" in original_node and "definition" in original_node["data"]:
            # Check if requires_approval is in the node definition
            requires_approval = original_node["data"]["definition"].get("requires_approval", False)

            # Check that the approval_required field matches
            assert transition.get("approval_required", False) == requires_approval, (
                f"Transition {transition['id']} has approval_required={transition.get('approval_required', False)}, "
                f"but node {original_node['id']} has requires_approval={requires_approval}"
            )


def test_start_node_ignored():
    """
    Test that the start node is ignored and nodes connected to it are marked as initial.
    """
    # Load the sample workflow
    with open(os.path.join("testing", "sample_workflow.json"), "r") as f:
        workflow_data = json.load(f)

    # Find the start node
    start_node_id = None
    for node in workflow_data["nodes"]:
        if (
            "data" in node
            and "definition" in node["data"]
            and "originalType" in node["data"]
            and node["data"]["originalType"] == "StartNode"
        ):
            start_node_id = node["id"]
            break

    assert start_node_id is not None, "Start node not found in the workflow"

    # Find nodes connected to the start node
    nodes_connected_to_start = []
    for edge in workflow_data["edges"]:
        if edge["source"] == start_node_id:
            nodes_connected_to_start.append(edge["target"])

    assert len(nodes_connected_to_start) > 0, "No nodes connected to the start node"

    # Convert the workflow to transition schema
    transition_schema = convert_workflow_to_transition_schema(workflow_data)

    # Check that the start node is not in the transitions
    for transition in transition_schema["transitions"]:
        assert transition["id"] != f"transition-{start_node_id}", "Start node should be ignored"

    # Check that nodes connected to the start node are marked as initial
    initial_transitions = []
    for transition in transition_schema["transitions"]:
        if transition["transition_type"] == "initial":
            # Extract the node ID from the transition ID
            transition_node_id = transition["id"].replace("transition-", "")
            initial_transitions.append(transition_node_id)

    # Check that at least one node connected to the start node is marked as initial
    assert any(
        node_id in initial_transitions for node_id in nodes_connected_to_start
    ), "No nodes connected to the start node are marked as initial"

    # Check that the start node is not in the nodes array
    for node in transition_schema["nodes"]:
        assert node["id"] != start_node_id, "Start node should not be in the nodes array"


def test_start_node_handles_set_to_null():
    """
    Test that handles attached to the start node are set to null.
    """
    # Load the sample workflow
    with open(os.path.join("testing", "sample_workflow.json"), "r") as f:
        workflow_data = json.load(f)

    # Find the start node
    start_node_id = None
    for node in workflow_data["nodes"]:
        if (
            "data" in node
            and "definition" in node["data"]
            and "originalType" in node["data"]
            and node["data"]["originalType"] == "StartNode"
        ):
            start_node_id = node["id"]
            break

    assert start_node_id is not None, "Start node not found in the workflow"

    # Find edges from the start node
    start_node_edges = []
    for edge in workflow_data["edges"]:
        if edge["source"] == start_node_id:
            start_node_edges.append(edge)

    assert len(start_node_edges) > 0, "No edges from the start node"

    # Convert the workflow to transition schema
    transition_schema = convert_workflow_to_transition_schema(workflow_data)

    # Check that field values connected to the start node are set to null
    for transition in transition_schema["transitions"]:
        transition_id = transition["id"].replace("transition-", "")

        # Check if this transition is connected to the start node
        for edge in start_node_edges:
            if edge["target"] == transition_id:
                target_handle = edge.get("targetHandle")

                # Check that the field value is null
                if (
                    target_handle
                    and "node_info" in transition
                    and "tools_to_use" in transition["node_info"]
                ):
                    for tool in transition["node_info"]["tools_to_use"]:
                        if "tool_params" in tool and "items" in tool["tool_params"]:
                            for item in tool["tool_params"]["items"]:
                                if item["field_name"] == target_handle:
                                    assert (
                                        item["field_value"] is None
                                    ), f"Field value for {target_handle} should be null, but got {item['field_value']}"


def test_loop_node_conversion():
    """
    Test that LoopNode from saved_config.json is correctly converted to loop transition schema.
    """
    # Load the saved_config.json that contains a LoopNode
    config_path = os.path.join("testing", "saved_config.json")
    if not os.path.exists(config_path):
        print(f"⚠️  Skipping LoopNode test: {config_path} not found")
        return

    with open(config_path, "r") as f:
        config = json.load(f)

    workflow_data = config["workflow_data"]

    # Convert the workflow to transition schema
    transition_schema = convert_workflow_to_transition_schema(workflow_data)

    # Find the loop transition
    loop_transition = None
    for transition in transition_schema["transitions"]:
        if transition.get("execution_type") == "loop":
            loop_transition = transition
            break

    assert loop_transition is not None, "No loop transition found in converted schema"

    # Validate loop_config structure
    assert "loop_config" in loop_transition, "Loop transition missing loop_config section"

    loop_config = loop_transition["loop_config"]

    # Check all required sections exist
    required_sections = [
        "iteration_behavior",
        "iteration_source",
        "exit_condition",
        "iteration_settings",
        "result_aggregation",
        "loop_body_configuration",
        "error_handling"
    ]

    for section in required_sections:
        assert section in loop_config, f"Missing required loop_config section: {section}"

    # Validate specific loop_config values
    assert loop_config["iteration_behavior"] == "independent", "Incorrect iteration_behavior"
    assert "number_range" in loop_config["iteration_source"], "Missing number_range in iteration_source"
    assert loop_config["exit_condition"]["condition_type"] == "all_items_processed", "Incorrect exit_condition"
    assert loop_config["result_aggregation"]["aggregation_type"] == "collect_all", "Incorrect aggregation_type"

    # Check loop_body_configuration structure
    lbc = loop_config["loop_body_configuration"]
    required_lbc_fields = [
        "entry_transitions",
        "exit_transitions",
        "chain_completion_detection",
        "chain_execution_timeout",
        "chain_monitoring",
        "auto_detection_config"
    ]

    for field in required_lbc_fields:
        assert field in lbc, f"Missing loop_body_configuration field: {field}"

    # Validate result_resolution for loop node
    assert "result_resolution" in loop_transition, "Loop transition missing result_resolution"

    result_res = loop_transition["result_resolution"]
    assert result_res.get("node_type") == "loop", f"Incorrect node_type: expected 'loop', got '{result_res.get('node_type')}'"

    # Check output handles
    handle_registry = result_res.get("handle_registry", {})
    output_handles = handle_registry.get("output_handles", [])

    expected_handles = ["current_item", "final_results"]
    actual_handles = [h.get("handle_id") for h in output_handles]

    for expected in expected_handles:
        assert expected in actual_handles, f"Missing output handle: {expected}"

    # Validate handle names
    for handle in output_handles:
        if handle.get("handle_id") == "current_item":
            expected_name = "Current Item (Iteration Output)"
            actual_name = handle.get("handle_name")
            assert actual_name == expected_name, f"Incorrect handle name for current_item: expected '{expected_name}', got '{actual_name}'"
        elif handle.get("handle_id") == "final_results":
            expected_name = "All Results (Exit Output)"
            actual_name = handle.get("handle_name")
            assert actual_name == expected_name, f"Incorrect handle name for final_results: expected '{expected_name}', got '{actual_name}'"

    print("✅ LoopNode conversion validation passed!")


if __name__ == "__main__":
    if PYTEST_AVAILABLE:
        pytest.main(["-xvs", __file__])
    else:
        pytest.main([])
