"""
Tests for the workflow schema converter.
"""

import json
import os
import sys

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
)

# Try to import pytest, but make it optional for direct execution
try:
    import pytest
    PYTEST_AVAILABLE = True
except ImportError:
    PYTEST_AVAILABLE = False
    # Mock pytest.main for direct execution
    class MockPytest:
        @staticmethod
        def main(args):
            print("pytest not available, running tests directly...")
            # Run all test functions
            test_functions = [
                test_loop_node_conversion  # Only run the LoopNode test for now
            ]
            for test_func in test_functions:
                try:
                    print(f"Running {test_func.__name__}...")
                    test_func()
                    print(f"✅ {test_func.__name__} PASSED")
                except Exception as e:
                    print(f"❌ {test_func.__name__} FAILED: {e}")
                    return 1
            print("🎉 All tests passed!")
            return 0
    pytest = MockPytest()


def test_approval_required_field():
    """
    Test that the approval_required field is correctly populated from the requires_approval field.
    """
    # Load the sample workflow
    with open(os.path.join("testing", "sample_workflow.json"), "r") as f:
        workflow_data = json.load(f)

    # Convert the workflow to transition schema
    transition_schema = convert_workflow_to_transition_schema(workflow_data)

    # Check that the transitions have the approval_required field
    for transition in transition_schema["transitions"]:
        node_id = transition["node_info"]["node_id"]
        transition_id = transition["id"].replace("transition-", "")

        # Find the corresponding node in the original workflow
        original_node = None

        # First, try to find the node by its ID matching the transition ID
        for node in workflow_data["nodes"]:
            if node["id"] == transition_id:
                original_node = node
                break

        # If we didn't find a node, try other methods
        if not original_node:
            for node in workflow_data["nodes"]:
                # Check if the node's server_id matches the node_id in the transition
                if (
                    "data" in node
                    and "definition" in node["data"]
                    and "mcp_info" in node["data"]["definition"]
                    and "server_id" in node["data"]["definition"]["mcp_info"]
                    and node["data"]["definition"]["mcp_info"]["server_id"] == node_id
                ):
                    original_node = node
                    break

                # Check if the node's definition name matches the node_id in the transition
                if (
                    "data" in node
                    and "definition" in node["data"]
                    and "name" in node["data"]["definition"]
                    and node["data"]["definition"]["name"] == node_id
                ):
                    original_node = node
                    break

        if original_node and "data" in original_node and "definition" in original_node["data"]:
            # Check if requires_approval is in the node definition
            requires_approval = original_node["data"]["definition"].get("requires_approval", False)

            # Check that the approval_required field matches
            assert transition.get("approval_required", False) == requires_approval, (
                f"Transition {transition['id']} has approval_required={transition.get('approval_required', False)}, "
                f"but node {original_node['id']} has requires_approval={requires_approval}"
            )


def test_start_node_ignored():
    """
    Test that the start node is ignored and nodes connected to it are marked as initial.
    """
    # Load the sample workflow
    with open(os.path.join("testing", "sample_workflow.json"), "r") as f:
        workflow_data = json.load(f)

    # Find the start node
    start_node_id = None
    for node in workflow_data["nodes"]:
        if (
            "data" in node
            and "definition" in node["data"]
            and "originalType" in node["data"]
            and node["data"]["originalType"] == "StartNode"
        ):
            start_node_id = node["id"]
            break

    assert start_node_id is not None, "Start node not found in the workflow"

    # Find nodes connected to the start node
    nodes_connected_to_start = []
    for edge in workflow_data["edges"]:
        if edge["source"] == start_node_id:
            nodes_connected_to_start.append(edge["target"])

    assert len(nodes_connected_to_start) > 0, "No nodes connected to the start node"

    # Convert the workflow to transition schema
    transition_schema = convert_workflow_to_transition_schema(workflow_data)

    # Check that the start node is not in the transitions
    for transition in transition_schema["transitions"]:
        assert transition["id"] != f"transition-{start_node_id}", "Start node should be ignored"

    # Check that nodes connected to the start node are marked as initial
    initial_transitions = []
    for transition in transition_schema["transitions"]:
        if transition["transition_type"] == "initial":
            # Extract the node ID from the transition ID
            transition_node_id = transition["id"].replace("transition-", "")
            initial_transitions.append(transition_node_id)

    # Check that at least one node connected to the start node is marked as initial
    assert any(
        node_id in initial_transitions for node_id in nodes_connected_to_start
    ), "No nodes connected to the start node are marked as initial"

    # Check that the start node is not in the nodes array
    for node in transition_schema["nodes"]:
        assert node["id"] != start_node_id, "Start node should not be in the nodes array"


def test_start_node_handles_set_to_null():
    """
    Test that handles attached to the start node are set to null.
    """
    # Load the sample workflow
    with open(os.path.join("testing", "sample_workflow.json"), "r") as f:
        workflow_data = json.load(f)

    # Find the start node
    start_node_id = None
    for node in workflow_data["nodes"]:
        if (
            "data" in node
            and "definition" in node["data"]
            and "originalType" in node["data"]
            and node["data"]["originalType"] == "StartNode"
        ):
            start_node_id = node["id"]
            break

    assert start_node_id is not None, "Start node not found in the workflow"

    # Find edges from the start node
    start_node_edges = []
    for edge in workflow_data["edges"]:
        if edge["source"] == start_node_id:
            start_node_edges.append(edge)

    assert len(start_node_edges) > 0, "No edges from the start node"

    # Convert the workflow to transition schema
    transition_schema = convert_workflow_to_transition_schema(workflow_data)

    # Check that field values connected to the start node are set to null
    for transition in transition_schema["transitions"]:
        transition_id = transition["id"].replace("transition-", "")

        # Check if this transition is connected to the start node
        for edge in start_node_edges:
            if edge["target"] == transition_id:
                target_handle = edge.get("targetHandle")

                # Check that the field value is null
                if (
                    target_handle
                    and "node_info" in transition
                    and "tools_to_use" in transition["node_info"]
                ):
                    for tool in transition["node_info"]["tools_to_use"]:
                        if "tool_params" in tool and "items" in tool["tool_params"]:
                            for item in tool["tool_params"]["items"]:
                                if item["field_name"] == target_handle:
                                    assert (
                                        item["field_value"] is None
                                    ), f"Field value for {target_handle} should be null, but got {item['field_value']}"


def test_loop_node_conversion():
    """
    Test that LoopNode from saved_config.json is correctly converted to loop transition schema.
    Save the output and compare with test.json format.
    """
    # Load the saved_config.json that contains a LoopNode
    config_path = os.path.join("testing", "saved_config.json")
    if not os.path.exists(config_path):
        print(f"⚠️  Skipping LoopNode test: {config_path} not found")
        return

    with open(config_path, "r") as f:
        config = json.load(f)

    workflow_data = config["workflow_data"]

    # Convert the workflow to transition schema
    transition_schema = convert_workflow_to_transition_schema(workflow_data)

    # Save the converted schema to a new file for comparison
    output_path = os.path.join("testing", "converted_schema_output.json")
    with open(output_path, "w") as f:
        json.dump(transition_schema, f, indent=2)
    print(f"📄 Converted schema saved to: {output_path}")

    # Find the loop transition
    loop_transition = None
    for transition in transition_schema["transitions"]:
        if transition.get("execution_type") == "loop":
            loop_transition = transition
            break

    assert loop_transition is not None, "No loop transition found in converted schema"

    # Save just the loop transition for detailed analysis
    loop_output_path = os.path.join("testing", "converted_loop_transition.json")
    with open(loop_output_path, "w") as f:
        json.dump(loop_transition, f, indent=2)
    print(f"🔄 Loop transition saved to: {loop_output_path}")

    # Load the reference test.json for comparison
    test_json_path = os.path.join("testing", "test.json")
    reference_schema = None
    reference_loop_transition = None

    if os.path.exists(test_json_path):
        with open(test_json_path, "r") as f:
            reference_schema = json.load(f)
        print(f"📋 Reference schema loaded from: {test_json_path}")

        # Find loop transition in reference schema
        for transition in reference_schema.get("transitions", []):
            if transition.get("execution_type") == "loop" or "loop_config" in transition:
                reference_loop_transition = transition
                break
    else:
        print(f"⚠️  Reference test.json not found at: {test_json_path}")
        # Try the orchestration engine path as backup
        backup_path = os.path.join("..", "..", "ruh-cataylst", "orchestration-engine", "testing", "test.json")
        if os.path.exists(backup_path):
            with open(backup_path, "r") as f:
                reference_schema = json.load(f)
            print(f"📋 Reference schema loaded from backup path: {backup_path}")

            # Find loop transition in reference schema
            for transition in reference_schema.get("transitions", []):
                if transition.get("execution_type") == "loop" or "loop_config" in transition:
                    reference_loop_transition = transition
                    break
        else:
            print(f"⚠️  Reference test.json not found at backup path either: {backup_path}")

    # Perform detailed comparison
    print("\n" + "="*80)
    print("🔍 DETAILED FORMAT COMPARISON")
    print("="*80)

    # Compare execution_type
    converted_exec_type = loop_transition.get("execution_type")
    print(f"📌 Execution Type:")
    print(f"   Converted: {converted_exec_type}")
    if reference_loop_transition:
        ref_exec_type = reference_loop_transition.get("execution_type")
        print(f"   Reference: {ref_exec_type}")
        if converted_exec_type != ref_exec_type:
            print(f"   ❌ MISMATCH: execution_type differs!")
        else:
            print(f"   ✅ MATCH: execution_type is correct")

    # Compare loop configuration structure
    print(f"\n📌 Loop Configuration Structure:")
    if "loop_config" in loop_transition:
        print(f"   ✅ Converted schema uses 'loop_config' structure")
        loop_config = loop_transition["loop_config"]
        print(f"   📋 loop_config sections: {list(loop_config.keys())}")
    else:
        print(f"   ❌ Converted schema missing 'loop_config' structure")

    if reference_loop_transition:
        if "loop_config" in reference_loop_transition:
            print(f"   ✅ Reference schema uses 'loop_config' structure")
            ref_loop_config = reference_loop_transition["loop_config"]
            print(f"   📋 Reference loop_config sections: {list(ref_loop_config.keys())}")
        elif "tool_params" in reference_loop_transition.get("node_info", {}).get("tools_to_use", [{}])[0]:
            print(f"   ⚠️  Reference schema uses old 'tool_params.items' structure")
            tool_params = reference_loop_transition["node_info"]["tools_to_use"][0]["tool_params"]
            if "items" in tool_params:
                items = tool_params["items"]
                print(f"   📋 Reference tool_params.items count: {len(items)}")
                item_names = [item.get("field_name") for item in items]
                print(f"   📋 Reference field names: {item_names}")

    # Validate our converted schema structure
    print(f"\n📌 Schema Structure Validation:")

    # Check if we have the correct structure
    has_loop_config = "loop_config" in loop_transition
    has_correct_exec_type = loop_transition.get("execution_type") == "loop"

    print(f"   ✅ Has loop_config: {has_loop_config}")
    print(f"   ✅ Has execution_type='loop': {has_correct_exec_type}")

    if has_loop_config:
        loop_config = loop_transition["loop_config"]
        required_sections = [
            "iteration_behavior", "iteration_source", "exit_condition",
            "iteration_settings", "result_aggregation", "loop_body_configuration", "error_handling"
        ]

        print(f"   📋 Required sections check:")
        for section in required_sections:
            has_section = section in loop_config
            print(f"      {'✅' if has_section else '❌'} {section}: {has_section}")

    # Check result_resolution
    print(f"\n📌 Result Resolution:")
    if "result_resolution" in loop_transition:
        result_res = loop_transition["result_resolution"]
        node_type = result_res.get("node_type")
        print(f"   ✅ Has result_resolution")
        print(f"   📋 node_type: {node_type}")

        if "handle_registry" in result_res:
            handle_registry = result_res["handle_registry"]
            output_handles = handle_registry.get("output_handles", [])
            handle_ids = [h.get("handle_id") for h in output_handles]
            print(f"   📋 Output handles: {handle_ids}")
        else:
            print(f"   ❌ Missing handle_registry")
    else:
        print(f"   ❌ Missing result_resolution")

    print("\n" + "="*80)
    print("📊 COMPARISON SUMMARY")
    print("="*80)

    if reference_loop_transition:
        # Determine if formats match
        converted_uses_loop_config = "loop_config" in loop_transition
        reference_uses_loop_config = "loop_config" in reference_loop_transition
        reference_uses_tool_params = "tool_params" in reference_loop_transition.get("node_info", {}).get("tools_to_use", [{}])[0]

        if converted_uses_loop_config and reference_uses_loop_config:
            print("✅ PERFECT MATCH: Both use loop_config structure")
        elif converted_uses_loop_config and reference_uses_tool_params:
            print("⚠️  FORMAT DIFFERENCE: Converted uses loop_config, Reference uses tool_params.items")
            print("   This suggests the reference schema is using the OLD format")
            print("   Our converted schema is using the NEW format (which is correct)")
        else:
            print("❌ FORMAT MISMATCH: Unexpected structure differences")
    else:
        print("⚠️  Cannot compare: Reference schema not available")

    print(f"\n🎯 CONCLUSION:")
    if has_loop_config and has_correct_exec_type:
        print("✅ Converted schema appears to be in the CORRECT format")
        print("✅ Uses loop_config structure as expected")
        print("✅ Has execution_type='loop' as expected")
    else:
        print("❌ Converted schema has issues")

    print("✅ LoopNode conversion analysis completed!")


if __name__ == "__main__":
    if PYTEST_AVAILABLE:
        pytest.main(["-xvs", __file__])
    else:
        pytest.main([])
