import json
from typing import List, Dict, Any, Optional
from app.utils.enhanced_logger import get_logger
from app.services.db_connections.db_initializer import get_db_connections

logger = get_logger("StateManager")


class WorkflowStateManager:
    """
    A dedicated class for managing the state of the workflow execution.
    Handles tracking of transition results, dependencies, and execution status.
    """

    def __init__(self, workflow_id=None, db_connections=None):
        self.workflow_id = workflow_id
        self.transition_results = {}
        self.completed_transitions = set()
        self.pending_transitions = set()
        self.waiting_transitions = set()
        self.transitions_waiting_for_approval: List[str] = []
        self.terminated = False
        self.workflow_paused = False
        self.logger = logger

        # Loop state management
        self.loop_states: Dict[str, Dict[str, Any]] = {}  # {loop_id: loop_state_data}
        self.active_loops: Dict[str, str] = {}  # {transition_id: loop_id}

        if db_connections:
            (
                self.results_redis_manager,
                self.state_redis_manager,
                self.postgres_manager,
                self.redis_event_listener,
            ) = db_connections
            self.logger.debug("Using provided database connections")
        else:
            # Get database connections from the initializer
            (
                self.results_redis_manager,
                self.state_redis_manager,
                self.postgres_manager,
                self.redis_event_listener,
            ) = get_db_connections()
            self.logger.debug("Using global database connections from initializer")

        # Update the workflow state manager reference in the Redis event listener
        if self.redis_event_listener:
            self.redis_event_listener.set_workflow_state_manager(self)
            self.logger.debug("Set workflow state manager reference in RedisEventListener")
            self.logger.debug(
                "Updated workflow state manager reference in Redis event listener"
            )

        # Log connection status
        if self.postgres_manager and self.postgres_manager.is_connected():
            self.logger.info("PostgreSQL connection available for persistent storage")
        else:
            self.logger.warning(
                "PostgreSQL connection unavailable, persistent storage limited"
            )

        self.logger.info("WorkflowStateManager initialized")

    def initialize_workflow(self, initial_transition_id):
        """
        Initialize the workflow with the first transition(s) to execute.

        Args:
            initial_transition_id: Can be a single transition ID string or a list of transition IDs.
        """
        # Handle both single transition ID and list of transition IDs
        if isinstance(initial_transition_id, list):
            self.pending_transitions = set(initial_transition_id)
            self.logger.info(
                f"Workflow initialized with multiple initial transitions: {initial_transition_id}"
            )
        else:
            self.pending_transitions = {initial_transition_id}
            self.logger.info(
                f"Workflow initialized with initial transition: {initial_transition_id}"
            )

        self.waiting_transitions = set()
        self.completed_transitions = set()
        self.transition_results = {}
        self.terminated = False

        self.logger.debug(
            f"State: pending={self.pending_transitions}, waiting={self.waiting_transitions}, completed={self.completed_transitions}"
        )

    def mark_transition_completed(self, transition_id, result=None):
        """Mark a transition as completed and store its result in Redis and PostgreSQL (with in-memory fallback)."""

        self.completed_transitions.add(transition_id)

        # Store in memory always
        if result is not None:
            self.transition_results[transition_id] = result
            result_summary = (
                str(result)[:100] + "..."
                if isinstance(result, str) and len(str(result)) > 100
                else result
            )
            self.logger.debug(
                f"Stored result for transition {transition_id} in memory: {result_summary}"
            )

        # Store in Redis only (PostgreSQL storage will happen via Redis event listener when keys expire)
        if result is not None:
            try:
                result_json = json.dumps(result)  # Serialize result to JSON string
                try:
                    if self.results_redis_manager.is_connected():
                        # Determine TTL based on transition type
                        ttl = self._get_result_ttl(transition_id)

                        self.results_redis_manager.set_value(
                            f"result:{transition_id}", result_json, ttl=ttl
                        )  # Store in Redis with appropriate TTL
                        self.logger.debug(
                            f"Stored result for transition {transition_id} in Redis with TTL {ttl}s. Will be archived to PostgreSQL when Redis key expires."
                        )
                    else:
                        self.logger.warning(
                            "Redis connection is not active, using in-memory fallback for results."
                        )
                        # If Redis is not available, we might want to consider direct PostgreSQL storage
                        # as a fallback, but for now we'll just use in-memory storage
                except Exception as redis_e:
                    self.logger.warning(
                        f"Error checking Redis connection or storing result for transition {transition_id}: {redis_e}. Using in-memory fallback."
                    )
            except Exception as e:
                self.logger.error(
                    f"Error serializing result for transition {transition_id}: {e}. Using in-memory fallback."
                )

        # Remove from pending or waiting if it's there
        was_pending = transition_id in self.pending_transitions
        was_waiting = transition_id in self.waiting_transitions

        self.pending_transitions.discard(transition_id)
        self.waiting_transitions.discard(transition_id)

        self.logger.info(
            f"Marked transition {transition_id} as completed (was_pending={was_pending}, was_waiting={was_waiting})"
        )
        self.logger.debug(
            f"Updated state: pending={self.pending_transitions}, waiting={self.waiting_transitions}, completed={self.completed_transitions}"
        )

    def get_transition_result(self, transition_id):
        """Get the result of a transition execution, prioritizing Redis, then PostgreSQL, then in-memory."""
        # Try to get from Redis first
        try:
            if self.results_redis_manager.is_connected():
                result_json = self.results_redis_manager.get_value(
                    f"result:{transition_id}"
                )
                if result_json:
                    try:
                        result_from_redis = json.loads(
                            result_json
                        )  # Deserialize from JSON string
                        self.logger.debug(
                            f"Retrieved result for transition {transition_id} from Redis"
                        )
                        # Extract actual result data from wrapped structure
                        extracted_result = self._extract_result_data(
                            result_from_redis, transition_id
                        )
                        return extracted_result
                    except json.JSONDecodeError as e:
                        self.logger.error(
                            f"Error decoding JSON result from Redis for transition {transition_id}: {e}. Trying PostgreSQL next."
                        )
                        # Continue to next storage option
                else:
                    self.logger.debug(
                        f"No result found in Redis for transition {transition_id}. Trying PostgreSQL next."
                    )
                    # Continue to next storage option
            else:
                self.logger.debug(
                    f"Redis is not connected, skipping Redis lookup for transition {transition_id}. Trying PostgreSQL next."
                )
        except Exception as e:
            self.logger.warning(
                f"Error checking Redis connection or retrieving result for transition {transition_id}: {e}. Trying PostgreSQL next."
            )

        # Try to get from PostgreSQL if not found in Redis or if Redis is not connected
        if (
            hasattr(self, "postgres_manager")
            and self.postgres_manager
            and self.postgres_manager.is_connected()
        ):
            result_from_postgres = self.postgres_manager.get_transition_result(
                self.workflow_id, transition_id
            )
            if result_from_postgres:
                self.logger.info(
                    f"Retrieved result for transition {transition_id} from PostgreSQL"
                )
                # Extract actual result data from wrapped structure
                extracted_result = self._extract_result_data(
                    result_from_postgres, transition_id
                )
                return extracted_result
            else:
                self.logger.debug(
                    f"No result found in PostgreSQL for transition {transition_id}. Trying in-memory next."
                )
                # Continue to in-memory fallback

        # Fallback to in-memory if Redis and PostgreSQL failed or no result found
        result_from_memory = self.transition_results.get(transition_id)
        if result_from_memory is not None:
            self.logger.info(f"Using in-memory result for transition {transition_id}.")
            # Extract actual result data from wrapped structure
            extracted_result = self._extract_result_data(
                result_from_memory, transition_id
            )
            return extracted_result
        else:
            self.logger.debug(
                f"No result found in Redis, PostgreSQL, or memory for transition {transition_id}"
            )
            return None

    def _extract_result_data(self, raw_result, transition_id):
        """
        Extract the actual result data from the wrapped structure created by TransitionHandler.

        The TransitionHandler wraps results in a structure like:
        {
            "tool_name": {
                "transition_id": "...",
                "node_id": "...",
                "tool_name": "...",
                "result": {"result": actual_data},
                "status": "completed",
                "timestamp": 1234567890
            }
        }

        This method extracts the actual_data for use by workflow utils.

        Args:
            raw_result: The raw result structure from storage
            transition_id: Transition ID for logging

        Returns:
            The extracted result data in the format expected by workflow utils
        """
        if not isinstance(raw_result, dict):
            self.logger.debug(
                f"Raw result is not a dict for transition {transition_id}, returning as-is"
            )
            return raw_result

        # Check if this is the wrapped structure from TransitionHandler
        # The structure should be: {tool_name: {wrapped_data}}
        if len(raw_result) == 1:
            tool_name, tool_result = next(iter(raw_result.items()))

            # Check if tool_result has the expected wrapped structure
            if (
                isinstance(tool_result, dict)
                and "result" in tool_result
                and "transition_id" in tool_result
                and "status" in tool_result
            ):

                self.logger.debug(
                    f"Detected wrapped result structure for transition {transition_id}, extracting data"
                )

                # Extract the nested result data but preserve the structure expected by workflow utils
                nested_result = tool_result.get("result", {})
                if isinstance(nested_result, dict) and "result" in nested_result:
                    # This is the double-nested structure: {"result": {"result": actual_data}}
                    # Return the nested_result to preserve the {"result": actual_data} structure
                    self.logger.debug(
                        f"Extracted double-nested result data for transition {transition_id}"
                    )
                    return nested_result  # Returns {"result": actual_data}
                else:
                    # This is single-nested: {"result": actual_data}
                    # Wrap it in the expected structure for consistency
                    self.logger.debug(
                        f"Extracted single-nested result data for transition {transition_id}"
                    )
                    return {"result": nested_result}  # Returns {"result": actual_data}

        # If it doesn't match the wrapped structure, check for multiple tools
        # In case of multiple tools, we need to return the whole structure
        # but still extract nested results if they exist
        extracted_results = {}
        for tool_name, tool_result in raw_result.items():
            if (
                isinstance(tool_result, dict)
                and "result" in tool_result
                and "transition_id" in tool_result
                and "status" in tool_result
            ):

                # Extract the nested result data for this tool but preserve structure
                nested_result = tool_result.get("result", {})
                if isinstance(nested_result, dict) and "result" in nested_result:
                    # Preserve the {"result": actual_data} structure
                    extracted_results[tool_name] = nested_result
                else:
                    # Wrap in expected structure for consistency
                    extracted_results[tool_name] = {"result": nested_result}
            else:
                # Not a wrapped structure, keep as-is
                extracted_results[tool_name] = tool_result

        if extracted_results:
            self.logger.debug(
                f"Extracted results for {len(extracted_results)} tools in transition {transition_id}"
            )
            return extracted_results

        # Fallback: return the original result if no extraction was possible
        self.logger.debug(
            f"No extraction needed for transition {transition_id}, returning original result"
        )
        return raw_result

    def reset_to_transition(
        self, start_transition_id, transitions_by_id, dependency_map
    ):
        """
        Resets the workflow state to start execution from a specific transition.
        Handles both "start from" and "re-execute" scenarios.
        Clears results from both Redis and in-memory storage for re-executed transitions.
        """
        if start_transition_id not in transitions_by_id:
            self.logger.error(
                f"Cannot reset to unknown transition ID: {start_transition_id}"
            )
            return

        if start_transition_id not in self.completed_transitions:
            self.logger.error(
                f"Cannot reset to transition {start_transition_id}. It is not in the list of completed transitions."
            )
            return False

        self.pending_transitions = {start_transition_id}

        if start_transition_id in self.completed_transitions:
            # Scenario: Re-execute a completed transition
            self.completed_transitions.remove(start_transition_id)

            # Clear in-memory result
            if start_transition_id in self.transition_results:
                del self.transition_results[start_transition_id]

            # Remove from Redis if connected
            if self.results_redis_manager.is_connected():
                self.results_redis_manager.delete_value(
                    f"result:{start_transition_id}"
                )  # Remove from Redis

            transitions_moved_to_waiting = set()

            for dependent_transition_id, dependencies in dependency_map.items():
                if start_transition_id in dependencies:  # Check for dependencies
                    if (
                        dependent_transition_id in self.pending_transitions
                    ):  # Move pending dependents to waiting
                        self.pending_transitions.discard(dependent_transition_id)
                        self.waiting_transitions.add(dependent_transition_id)
                        transitions_moved_to_waiting.add(dependent_transition_id)
                        self.logger.info(
                            f"Moved pending transition {dependent_transition_id} to waiting because it depends on re-run transition {start_transition_id}"
                        )
                    elif dependent_transition_id in self.completed_transitions:
                        self.completed_transitions.discard(dependent_transition_id)
                        self.waiting_transitions.add(dependent_transition_id)
                        transitions_moved_to_waiting.add(dependent_transition_id)
                        self.logger.info(
                            f"Moved COMPLETED dependent transition {dependent_transition_id} to waiting because it depends on re-run transition {start_transition_id}"
                        )

            self.logger.info(
                f"Transition {start_transition_id} marked for re-execution. "
                f"Cleared results (Redis and memory). Moved {len(transitions_moved_to_waiting)} dependent pending transitions to waiting: {transitions_moved_to_waiting}"
            )

        self.logger.debug(
            f"State after reset: pending={self.pending_transitions}, waiting={self.waiting_transitions}, completed={self.completed_transitions}, terminated={self.terminated}"
        )

    def are_dependencies_met(self, transition_dependencies):
        """
        Check if all dependencies for a transition are met.

        Args:
            transition_dependencies: List of transition IDs that need to be completed
                                    before this transition can execute.

        Returns:
            bool: True if all dependencies are met, False otherwise.
        """
        all_met = all(
            dep_id in self.completed_transitions for dep_id in transition_dependencies
        )

        if not all_met:
            missing_deps = [
                dep_id
                for dep_id in transition_dependencies
                if dep_id not in self.completed_transitions
            ]
            self.logger.debug(f"Dependencies not met. Missing: {missing_deps}")
        else:
            self.logger.debug(f"All dependencies met for: {transition_dependencies}")

        return all_met

    def update_pending_transitions(self, new_transitions):
        """
        Update the set of pending transitions based on dependency status.

        Args:
            new_transitions: List of transition IDs to potentially add to pending.
        """
        added_transitions = []
        for transition_id in new_transitions:
            if transition_id not in self.completed_transitions:
                if transition_id not in self.pending_transitions:
                    added_transitions.append(transition_id)
                self.pending_transitions.add(transition_id)

        if added_transitions:
            self.logger.info(f"Added transitions to pending: {added_transitions}")
            self.logger.debug(
                f"Updated pending transitions: {self.pending_transitions}"
            )

    def move_waiting_to_pending(self, transition_dependencies_map):
        """
        Move transitions from waiting to pending if their dependencies are now met.

        Args:
            transition_dependencies_map: Dictionary mapping transition IDs to their dependencies.

        Returns:
            set: Set of transitions that were moved from waiting to pending.
        """
        transitions_ready = set()

        self.logger.debug(f"Checking waiting transitions: {self.waiting_transitions}")
        for transition_id in list(self.waiting_transitions):
            dependencies = transition_dependencies_map.get(transition_id, [])
            if all(dep_id in self.completed_transitions for dep_id in dependencies):
                transitions_ready.add(transition_id)
                self.logger.debug(
                    f"Transition {transition_id} is now ready (dependencies met: {dependencies})"
                )

        # Update the sets
        if transitions_ready:
            self.waiting_transitions -= transitions_ready
            self.pending_transitions.update(transitions_ready)
            self.logger.info(
                f"Moved transitions from waiting to pending: {transitions_ready}"
            )
            self.logger.debug(
                f"Updated waiting={self.waiting_transitions}, pending={self.pending_transitions}"
            )
        else:
            self.logger.debug("No waiting transitions are ready to move to pending")

        return transitions_ready

    def get_pending_transitions(self):
        """Get the set of pending transitions."""
        pending_copy = self.pending_transitions.copy()
        self.logger.debug(f"Retrieved pending transitions: {pending_copy}")
        return pending_copy

    def clear_pending_transitions(self):
        """Clear the set of pending transitions."""
        pending_copy = self.pending_transitions.copy()
        self.pending_transitions.clear()
        self.logger.info(
            f"Cleared {len(pending_copy)} pending transitions: {pending_copy}"
        )
        return pending_copy

    def set_terminated(self, value=True):
        """Set the terminated flag."""
        self.terminated = value
        self.logger.info(f"Workflow terminated flag set to: {value}")

    def is_workflow_active(self):
        """Check if the workflow is still active (has pending or waiting transitions)."""
        active = not self.terminated and (
            self.pending_transitions or self.waiting_transitions
        )
        status_reason = []
        if self.terminated:
            status_reason.append("terminated=True")
        if not self.pending_transitions:
            status_reason.append("pending=empty")
        if not self.waiting_transitions:
            status_reason.append("waiting=empty")

        status_msg = f"Workflow active: {active}" + (
            f" ({', '.join(status_reason)})" if not active else ""
        )
        self.logger.debug(status_msg)
        return active

    def extract_dependencies(self, transition):
        """
        Extract the dependencies (input_data) from a transition.

        Args:
            transition: The transition object.

        Returns:
            list: List of transition IDs that this transition depends on.
        """
        dependencies = []
        input_data_configs = transition.get("node_info", {}).get("input_data", [])

        for input_data_config in input_data_configs:
            from_transition_id = input_data_config.get("from_transition_id")
            if from_transition_id:
                dependencies.append(from_transition_id)

        if dependencies:
            self.logger.debug(
                f"Extracted dependencies for transition {transition.get('id', 'unknown')}: {dependencies}"
            )

        return dependencies

    def get_dependency_map(self, transitions_by_id):
        """
        Build a map of transition IDs to their dependencies.

        Args:
            transitions_by_id: Dictionary mapping transition IDs to transition objects.

        Returns:
            dict: Dictionary mapping transition IDs to lists of transition IDs they depend on.
        """
        dependency_map = {}

        for transition_id, transition in transitions_by_id.items():
            dependency_map[transition_id] = self.extract_dependencies(transition)

        self.logger.info(f"Built dependency map for {len(dependency_map)} transitions")
        for t_id, deps in dependency_map.items():
            if deps:
                self.logger.debug(f"Transition {t_id} depends on: {deps}")

        return dependency_map

    def log_workflow_state(self):
        """
        Log the current state of the workflow for debugging purposes.
        """
        self.logger.info("=== WORKFLOW STATE SNAPSHOT ===")
        self.logger.info(f"Terminated: {self.terminated}")
        self.logger.info(
            f"Pending transitions ({len(self.pending_transitions)}): {sorted(list(self.pending_transitions))}"
        )
        self.logger.info(
            f"Waiting transitions ({len(self.waiting_transitions)}): {sorted(list(self.waiting_transitions))}"
        )
        self.logger.info(
            f"Completed transitions ({len(self.completed_transitions)}): {sorted(list(self.completed_transitions))}"
        )
        self.logger.info(
            f"Results stored for {len(self.transition_results)} transitions"
        )
        self.logger.info(
            f"Workflow status: {'active' if self.is_workflow_active() else 'inactive'}"
        )
        self.logger.info(
            f"Workflow status: {'active' if self.is_workflow_active() else 'inactive'}"
        )
        self.logger.info(f"Workflow paused: {self.workflow_paused}")
        self.logger.info("==============================")

    async def save_workflow_state(self):
        """Saves the current workflow state to Redis and PostgreSQL databases."""
        state = {
            "pending_transitions": list(self.pending_transitions),
            "completed_transitions": list(self.completed_transitions),
            "waiting_transitions": list(self.waiting_transitions),
            "terminated": self.terminated,
            "paused": self.workflow_paused,
            "loop_states": self.loop_states,
            "active_loops": self.active_loops,
        }
        state_json = json.dumps(state)
        redis_key = f"workflow_state:{self.workflow_id}"

        # Save to Redis only (PostgreSQL storage will happen via Redis event listener when keys expire)
        redis_success = False
        try:
            if self.state_redis_manager.is_connected():
                self.state_redis_manager.set_value(redis_key, state_json)
                self.logger.info(
                    f"Workflow state saved to Redis for workflow ID: {self.workflow_id}. Will be archived to PostgreSQL when Redis key expires."
                )
                redis_success = True
            else:
                self.logger.warning(
                    f"Redis (state DB) connection inactive for workflow ID: {self.workflow_id}. State will only be stored in memory."
                )
                # If Redis is not available, we might want to consider direct PostgreSQL storage
                # as a fallback, but for now we'll just use in-memory storage
        except Exception as e:
            self.logger.error(
                f"Error saving workflow state to Redis (state DB) for workflow ID {self.workflow_id}: {e}"
            )

        return redis_success

    async def load_workflow_state(self):
        """Loads and restores the workflow state from Redis or PostgreSQL database."""
        redis_key = f"workflow_state:{self.workflow_id}"

        # Try to load from Redis first
        try:
            if self.state_redis_manager.is_connected():
                state_json = self.state_redis_manager.get_value(redis_key)
                if state_json:
                    state = json.loads(state_json)
                    self.pending_transitions = set(state.get("pending_transitions", []))
                    self.completed_transitions = set(
                        state.get("completed_transitions", [])
                    )
                    self.waiting_transitions = set(state.get("waiting_transitions", []))
                    self.terminated = state.get("terminated", False)
                    self.workflow_paused = state.get("paused", False)
                    self.loop_states = state.get("loop_states", {})
                    self.active_loops = state.get("active_loops", {})
                    self.logger.info(
                        f"Workflow state loaded from Redis for workflow ID: {self.workflow_id}"
                    )
                    self.is_workflow_active()
                    return True
                else:
                    self.logger.info(
                        f"No saved workflow state found in Redis for workflow ID: {self.workflow_id}. Trying PostgreSQL."
                    )
                    # Continue to PostgreSQL
            else:
                self.logger.warning(
                    f"Redis connection inactive, trying PostgreSQL for workflow ID: {self.workflow_id}"
                )
                # Continue to PostgreSQL
        except (json.JSONDecodeError, Exception) as e:
            self.logger.error(
                f"Error loading workflow state from Redis for workflow ID {self.workflow_id}: {e}. Trying PostgreSQL."
            )
            # Continue to PostgreSQL

        # Try to load from PostgreSQL if not found in Redis or if Redis is not connected
        try:
            if (
                hasattr(self, "postgres_manager")
                and self.postgres_manager
                and self.postgres_manager.is_connected()
            ):
                state_data = self.postgres_manager.get_workflow_state(self.workflow_id)
                if state_data and "state_data" in state_data:
                    state = state_data["state_data"]
                    self.pending_transitions = set(state.get("pending_transitions", []))
                    self.completed_transitions = set(
                        state.get("completed_transitions", [])
                    )
                    self.waiting_transitions = set(state.get("waiting_transitions", []))
                    self.terminated = state.get("terminated", False)
                    self.workflow_paused = state.get("paused", False)
                    self.loop_states = state.get("loop_states", {})
                    self.active_loops = state.get("active_loops", {})
                    self.logger.info(
                        f"Workflow state loaded from PostgreSQL for workflow ID: {self.workflow_id}"
                    )
                    self.is_workflow_active()
                    return True
                else:
                    self.logger.info(
                        f"No saved workflow state found in PostgreSQL for workflow ID: {self.workflow_id}. Starting fresh."
                    )
            else:
                self.logger.warning(
                    f"PostgreSQL connection inactive, cannot load workflow state for workflow ID: {self.workflow_id}. Starting fresh."
                )
        except Exception as e:
            self.logger.error(
                f"Error loading workflow state from PostgreSQL for workflow ID {self.workflow_id}: {e}. Starting fresh."
            )

        return False

    def archive_transition_result(self, transition_id, result=None):
        """Archive a transition result to PostgreSQL."""
        self.logger.debug(
            f"Attempting to archive result for transition {transition_id}"
        )

        if (
            not hasattr(self, "postgres_manager")
            or not self.postgres_manager
            or not self.postgres_manager.is_connected()
        ):
            self.logger.warning(
                "PostgreSQL is not connected. Cannot archive transition result."
            )
            return False

        # Use provided result if available
        result_to_archive = result
        self.logger.debug(f"Provided result: {result_to_archive is not None}")

        # If no result provided, try to get from Redis
        if result_to_archive is None:
            try:
                if self.results_redis_manager.is_connected():
                    self.logger.debug(
                        f"Trying to get result from Redis for transition {transition_id}"
                    )
                    result_json = self.results_redis_manager.get_value(
                        f"result:{transition_id}"
                    )
                    if result_json:
                        try:
                            result_to_archive = json.loads(result_json)
                            self.logger.debug(
                                f"Found result in Redis for transition {transition_id}"
                            )
                        except json.JSONDecodeError as e:
                            self.logger.error(
                                f"Error decoding JSON result from Redis for transition {transition_id}: {e}"
                            )
                    else:
                        self.logger.debug(
                            f"No result found in Redis for transition {transition_id}"
                        )
                else:
                    self.logger.debug(
                        f"Redis is not connected, skipping Redis lookup for transition {transition_id}"
                    )
            except Exception as e:
                self.logger.warning(
                    f"Error checking Redis connection or retrieving result for transition {transition_id}: {e}"
                )

        # If still no result, try to get from memory
        if result_to_archive is None:
            self.logger.debug(
                f"Trying to get result from memory for transition {transition_id}"
            )
            result_to_archive = self.transition_results.get(transition_id)
            if result_to_archive:
                self.logger.debug(
                    f"Found result in memory for transition {transition_id}"
                )
            else:
                self.logger.debug(
                    f"No result found in memory for transition {transition_id}"
                )
                self.logger.debug(
                    f"Available transition results in memory: {list(self.transition_results.keys())}"
                )

        if result_to_archive is not None:
            self.logger.debug(
                f"Archiving result to PostgreSQL for transition {transition_id}"
            )
            success = self.postgres_manager.store_transition_result(
                self.workflow_id, transition_id, result_to_archive
            )
            if success:
                self.logger.info(
                    f"Archived result for transition {transition_id} to PostgreSQL"
                )
            else:
                self.logger.error(
                    f"Failed to archive result for transition {transition_id} to PostgreSQL"
                )
            return success

        # Check if this is a loop iteration result that can be safely ignored
        if self._is_loop_iteration_result(transition_id):
            self.logger.debug(
                f"No result found to archive for loop iteration transition {transition_id} (this is expected for temporary loop data)"
            )
        else:
            self.logger.warning(
                f"No result found to archive for transition {transition_id}"
            )
        return False

    def _is_loop_iteration_result(self, transition_id: str) -> bool:
        """
        Check if a transition ID represents a loop iteration result.

        Args:
            transition_id: The transition ID to check

        Returns:
            bool: True if this is a loop iteration result, False otherwise
        """
        loop_patterns = [
            "loop_iteration_",
            "current_iteration",
            "loop_state_",
            "iteration_payload_"
        ]
        return any(pattern in transition_id for pattern in loop_patterns)

    def archive_workflow_state(self):
        """Archive the current workflow state to PostgreSQL."""
        if (
            not hasattr(self, "postgres_manager")
            or not self.postgres_manager
            or not self.postgres_manager.is_connected()
        ):
            self.logger.warning(
                "PostgreSQL is not connected. Cannot archive workflow state."
            )
            return False

        # Prepare state data
        state = {
            "pending_transitions": list(self.pending_transitions),
            "completed_transitions": list(self.completed_transitions),
            "waiting_transitions": list(self.waiting_transitions),
            "terminated": self.terminated,
            "paused": self.workflow_paused,
            "loop_states": self.loop_states,
            "active_loops": self.active_loops,
        }

        success = self.postgres_manager.store_workflow_state(
            self.workflow_id, self.workflow_id, state
        )

        if success:
            self.logger.info(
                f"Archived workflow state to PostgreSQL for workflow ID: {self.workflow_id}"
            )

        return success

    # Loop State Management Methods

    def store_loop_state(
        self, loop_id: str, transition_id: str, loop_state_data: Dict[str, Any]
    ) -> None:
        """
        Store loop state data for a specific loop.

        Args:
            loop_id: Unique identifier for the loop
            transition_id: ID of the transition containing the loop
            loop_state_data: Complete loop state data dictionary
        """
        self.loop_states[loop_id] = loop_state_data
        self.active_loops[transition_id] = loop_id
        self.logger.debug(
            f"Stored loop state for loop_id: {loop_id}, transition_id: {transition_id}"
        )

    def get_loop_state(self, loop_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve loop state data for a specific loop.

        Args:
            loop_id: Unique identifier for the loop

        Returns:
            Loop state data dictionary or None if not found
        """
        loop_state = self.loop_states.get(loop_id)
        if loop_state:
            self.logger.debug(f"Retrieved loop state for loop_id: {loop_id}")
        return loop_state

    def get_loop_state_by_transition(
        self, transition_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve loop state data for a loop associated with a transition.

        Args:
            transition_id: ID of the transition containing the loop

        Returns:
            Loop state data dictionary or None if not found
        """
        loop_id = self.active_loops.get(transition_id)
        if loop_id:
            return self.get_loop_state(loop_id)
        return None

    def remove_loop_state(
        self, loop_id: str, transition_id: Optional[str] = None
    ) -> bool:
        """
        Remove loop state data for a specific loop.

        Args:
            loop_id: Unique identifier for the loop
            transition_id: Optional transition ID to remove from active loops

        Returns:
            True if loop state was removed, False if not found
        """
        removed = False

        if loop_id in self.loop_states:
            del self.loop_states[loop_id]
            removed = True
            self.logger.debug(f"Removed loop state for loop_id: {loop_id}")

        if transition_id and transition_id in self.active_loops:
            del self.active_loops[transition_id]
            self.logger.debug(
                f"Removed active loop mapping for transition_id: {transition_id}"
            )

        return removed

    def get_all_loop_states(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all loop states for the workflow.

        Returns:
            Dictionary mapping loop_ids to loop state data
        """
        return self.loop_states.copy()

    def is_loop_active(self, transition_id: str) -> bool:
        """
        Check if a loop is active for a specific transition.

        Args:
            transition_id: ID of the transition to check

        Returns:
            True if a loop is active for the transition, False otherwise
        """
        return transition_id in self.active_loops

    def get_active_loop_id(self, transition_id: str) -> Optional[str]:
        """
        Get the active loop ID for a specific transition.

        Args:
            transition_id: ID of the transition

        Returns:
            Loop ID if active, None otherwise
        """
        return self.active_loops.get(transition_id)

    def _get_result_ttl(self, transition_id: str) -> Optional[int]:
        """
        Determine the appropriate TTL for a result based on the transition ID.

        Args:
            transition_id: The transition ID to determine TTL for

        Returns:
            TTL in seconds, or None to use default
        """
        from app.config.config import settings

        # Check if this is a loop iteration result that needs longer TTL
        loop_patterns = [
            "loop_iteration_",
            "current_iteration",
            "loop_state_",
            "iteration_payload_"
        ]

        if any(pattern in transition_id for pattern in loop_patterns):
            # Give loop iteration results longer TTL to ensure they can be archived
            # Use 3x the default results TTL for loop iterations
            default_ttl = settings.redis_results_ttl
            extended_ttl = default_ttl * 3
            self.logger.debug(f"Using extended TTL {extended_ttl}s for loop iteration result: {transition_id}")
            return extended_ttl

        # Use default TTL for regular transitions
        return None
