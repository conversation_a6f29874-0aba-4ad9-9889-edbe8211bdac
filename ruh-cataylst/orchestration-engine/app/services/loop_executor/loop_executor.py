import asyncio
import traceback
import uuid
from typing import Dict, List, Any, Optional, Union, Tuple
from app.utils.enhanced_logger import get_logger
from .loop_state_manager import LoopStateManager
from .loop_aggregator import LoopAggregator
from .loop_error_handler import <PERSON><PERSON>rror<PERSON>andler
from .loop_exit_condition_evaluator import LoopExitConditionEvaluator
from .loop_body_chain_executor import LoopBodyChainExecutor


class LoopExecutor:
    """
    Loop Executor for handling internal loop execution within the orchestration engine.

    This executor manages loop iterations, context preservation, result aggregation,
    and concurrent execution with index-based result reassembly.
    """

    def __init__(
        self,
        state_manager,
        workflow_utils,
        result_callback,
        transitions_by_id: Dict[str, Dict],
        nodes: Dict[str, Dict],
        transition_handler,
        user_id: Optional[str] = None,
    ):
        """
        Initialize the LoopExecutor with required dependencies.

        Args:
            state_manager: WorkflowStateManager instance for state tracking
            workflow_utils: WorkflowUtils instance for utility functions
            result_callback: Callback function for result reporting
            transitions_by_id: Dictionary mapping transition IDs to transition objects
            nodes: Dictionary mapping node IDs to node objects
            transition_handler: TransitionHandler instance for executing loop body transitions
            user_id: Optional user ID for execution context
        """
        self.state_manager = state_manager
        self.workflow_utils = workflow_utils
        self.result_callback = result_callback
        self.transitions_by_id = transitions_by_id
        self.nodes = nodes
        self.transition_handler = transition_handler
        self.user_id = user_id
        self.logger = get_logger("LoopExecutor")

        # Initialize aggregator, error handler, and exit condition evaluator
        self.aggregator = LoopAggregator()
        self.error_handler = LoopErrorHandler(self.logger)
        self.exit_condition_evaluator = LoopExitConditionEvaluator()

        # Initialize loop body chain executor
        self.loop_body_chain_executor = LoopBodyChainExecutor(
            state_manager=self.state_manager,
            transition_handler=self.transition_handler,
            workflow_utils=self.workflow_utils,
            transitions_by_id=self.transitions_by_id,
            user_id=self.user_id
        )

        # Loop execution state
        self.current_loop_config = None
        self.current_iteration_data = []
        self.iteration_results = {}
        self.loop_context = {}
        self.loop_state_manager = None  # Will be initialized when loop starts

        # Phase 4: Orchestration Engine Integration
        self.orchestration_engine = None

    async def execute_tool(
        self,
        tool_name: str = None,
        tool_parameters: dict = None,
        loop_config: dict = None,
        transition_id: str = None,
        input_data: dict = None,
        output_routing: dict = None,
        input_data_configs: list = None,
        loop_state: dict = None,
    ) -> Dict[str, Any]:
        """
        Execute a loop node with dual output routing (iteration and exit outputs).

        Loop nodes don't execute tools - they manage iteration logic and route data
        through two outputs: iteration output (current item) and exit output (final results).

        Args:
            tool_name: Not used for loop nodes (kept for interface compatibility)
            tool_parameters: Not used for loop nodes (kept for interface compatibility)
            loop_config: Loop configuration dictionary
            transition_id: ID of the loop transition being executed
            input_data: Input data for the loop node
            output_routing: Output routing configuration for iteration and exit outputs

        Returns:
            Dictionary with iteration and exit output data
        """
        try:
            self.logger.info(
                f"🔄 Starting loop node execution for transition: {transition_id}"
            )

            # Parse and validate loop configuration
            parsed_config = self.parse_loop_config(loop_config)
            self.validate_loop_config(parsed_config)
            self.current_loop_config = parsed_config

            # Store input data and output routing
            self.input_data = input_data or {}
            self.output_routing = output_routing or {}
            self.input_data_configs = input_data_configs or []

            # Store current transition ID for chain executor context
            self._current_transition_id = transition_id

            # Register with transition handler for orchestration integration
            if self.transition_handler and hasattr(self.transition_handler, 'register_loop_executor'):
                self.transition_handler.register_loop_executor(transition_id, self)
                self.logger.debug(
                    f"📝 Registered loop executor with transition handler for transition: {transition_id}"
                )

            # STEP 1: Config parsing - Already done above with parse_loop_config()

            # STEP 2: Loop state initialization - Initialize loop state for this specific loop
            loop_state = await self._initialize_loop_state_for_workflow(parsed_config, transition_id, input_data)
            if not loop_state:
                return {"error": "Failed to initialize loop state", "success": False}

            # STEP 3-6: Execute the complete loop workflow
            loop_results = await self._execute_loop_workflow(loop_state)

            self.logger.info(
                f"✅ Loop node execution completed for transition: {transition_id}"
            )
            return loop_results

        except Exception as e:
            self.logger.error(
                f"❌ Loop node execution failed for transition {transition_id}: {str(e)}"
            )
            await self.handle_loop_error(e, transition_id)
            raise

        finally:
            # Store current transition ID for cleanup
            self._current_transition_id = transition_id

            # Comprehensive cleanup of loop resources
            await self.cleanup_loop_resources()

            # Clear current transition ID
            self._current_transition_id = None

    # Phase 3: Advanced Parallel Processing Methods

    async def _initialize_adaptive_concurrency(self, concurrency_config: Dict[str, Any]) -> None:
        """Initialize adaptive concurrency control system."""

        adaptive_config = concurrency_config.get("adaptive_settings", {})

        self.concurrent_state["adaptive_metrics"].update({
            "target_throughput": adaptive_config.get("target_throughput", 10.0),
            "adjustment_interval": adaptive_config.get("adjustment_interval", 30.0),
            "min_concurrent": adaptive_config.get("min_concurrent", 1),
            "max_concurrent": adaptive_config.get("max_concurrent", 20),
            "performance_window": adaptive_config.get("performance_window", 100),
            "adjustment_factor": adaptive_config.get("adjustment_factor", 0.1)
        })

        self.logger.info(
            f"🧠 Initialized adaptive concurrency: target_throughput={adaptive_config.get('target_throughput', 10.0)}, "
            f"range=[{adaptive_config.get('min_concurrent', 1)}, {adaptive_config.get('max_concurrent', 20)}]"
        )

    async def _initialize_circuit_breaker(self, circuit_breaker_config: Dict[str, Any]) -> None:
        """Initialize circuit breaker for fault tolerance."""

        self.concurrent_state["circuit_breaker_state"].update({
            "failure_threshold": circuit_breaker_config.get("failure_threshold", 5),
            "recovery_timeout": circuit_breaker_config.get("recovery_timeout", 60.0),
            "success_threshold": circuit_breaker_config.get("success_threshold", 3),
            "failure_rate_threshold": circuit_breaker_config.get("failure_rate_threshold", 0.5)
        })

        self.logger.info(
            f"🔒 Initialized circuit breaker: failure_threshold={circuit_breaker_config.get('failure_threshold', 5)}, "
            f"recovery_timeout={circuit_breaker_config.get('recovery_timeout', 60.0)}s"
        )

    async def _check_circuit_breaker(self) -> bool:
        """Check if circuit breaker allows execution."""

        cb_state = self.concurrent_state["circuit_breaker_state"]
        current_time = asyncio.get_event_loop().time()

        if cb_state["state"] == "open":
            # Check if recovery timeout has passed
            if current_time - cb_state["last_failure_time"] > cb_state["recovery_timeout"]:
                cb_state["state"] = "half_open"
                cb_state["success_count"] = 0
                self.logger.info("🔓 Circuit breaker moved to half-open state")
                return True
            else:
                return False

        elif cb_state["state"] == "half_open":
            # Allow limited execution to test recovery
            return True

        else:  # closed
            return True

    async def _record_circuit_breaker_success(self) -> None:
        """Record successful execution for circuit breaker."""

        cb_state = self.concurrent_state["circuit_breaker_state"]

        if cb_state["state"] == "half_open":
            cb_state["success_count"] += 1
            if cb_state["success_count"] >= cb_state["success_threshold"]:
                cb_state["state"] = "closed"
                cb_state["failure_count"] = 0
                self.logger.info("✅ Circuit breaker closed - service recovered")

    async def _record_circuit_breaker_failure(self, error: Exception) -> None:
        """Record failed execution for circuit breaker."""

        cb_state = self.concurrent_state["circuit_breaker_state"]
        current_time = asyncio.get_event_loop().time()

        cb_state["failure_count"] += 1
        cb_state["last_failure_time"] = current_time

        if cb_state["state"] == "half_open":
            # Failure during recovery - back to open
            cb_state["state"] = "open"
            self.logger.warning("🔒 Circuit breaker opened - recovery failed")

        elif cb_state["failure_count"] >= cb_state["failure_threshold"]:
            # Too many failures - open circuit
            cb_state["state"] = "open"
            self.logger.warning(
                f"🔒 Circuit breaker opened - {cb_state['failure_count']} failures exceeded threshold"
            )

    async def _update_adaptive_metrics(self, execution_time: float, success: bool, iteration_index: int) -> None:
        """Update adaptive concurrency metrics."""

        metrics = self.concurrent_state["adaptive_metrics"]
        current_time = asyncio.get_event_loop().time()

        # Update average execution time with exponential moving average
        alpha = 0.1  # Smoothing factor
        if metrics["avg_execution_time"] == 0.0:
            metrics["avg_execution_time"] = execution_time
        else:
            metrics["avg_execution_time"] = (
                alpha * execution_time + (1 - alpha) * metrics["avg_execution_time"]
            )

        # Update success rate
        total_completed = self.concurrent_state["completed_count"] + self.concurrent_state["failed_count"]
        if total_completed > 0:
            metrics["success_rate"] = self.concurrent_state["completed_count"] / total_completed

        # Update throughput (iterations per second)
        elapsed_time = current_time - self.concurrent_state["start_time"]
        if elapsed_time > 0:
            metrics["throughput"] = total_completed / elapsed_time

        # Check if adjustment is needed
        if current_time - metrics["last_adjustment"] > metrics["adjustment_interval"]:
            await self._adjust_adaptive_concurrency()
            metrics["last_adjustment"] = current_time

    async def _adjust_adaptive_concurrency(self) -> None:
        """Adjust concurrency based on performance metrics."""

        metrics = self.concurrent_state["adaptive_metrics"]
        current_concurrent = self.current_loop_config["concurrency"]["max_concurrent"]

        target_throughput = metrics["target_throughput"]
        actual_throughput = metrics["throughput"]
        success_rate = metrics["success_rate"]

        # Calculate adjustment based on performance
        if success_rate < 0.8:  # Too many failures
            adjustment = -1
            reason = f"low success rate ({success_rate:.2f})"
        elif actual_throughput < target_throughput * 0.8:  # Underperforming
            adjustment = 1
            reason = f"low throughput ({actual_throughput:.2f} < {target_throughput})"
        elif actual_throughput > target_throughput * 1.2:  # Overperforming
            adjustment = 1
            reason = f"high throughput ({actual_throughput:.2f} > {target_throughput})"
        else:
            return  # No adjustment needed

        # Apply adjustment within bounds
        new_concurrent = max(
            metrics["min_concurrent"],
            min(metrics["max_concurrent"], current_concurrent + adjustment)
        )

        if new_concurrent != current_concurrent:
            self.current_loop_config["concurrency"]["max_concurrent"] = new_concurrent
            self.logger.info(
                f"🔧 Adaptive concurrency: {current_concurrent} → {new_concurrent} ({reason})"
            )

    async def _select_worker_pool(self, load_balancing: str) -> Optional[str]:
        """Select worker pool based on load balancing strategy."""

        if load_balancing == "round_robin":
            return None  # Use default execution

        # For future implementation of worker pools
        return None

    async def _allocate_resources(self, iteration_index: int, iteration_item: Any) -> Dict[str, Any]:
        """Allocate resources for iteration execution."""

        # Basic resource allocation - can be enhanced based on iteration requirements
        return {
            "memory_limit": "default",
            "cpu_priority": "normal",
            "io_priority": "normal"
        }

    async def _release_resources(self, task_id: str) -> None:
        """Release allocated resources for completed task."""

        # Basic cleanup - can be enhanced for specific resource types
        pass

    def parse_loop_config(self, loop_config: dict) -> dict:
        """
        Parse and extract loop configuration parameters, converting user-friendly schema to internal format.

        Args:
            loop_config: Raw loop configuration dictionary (either old format or new user-friendly format)

        Returns:
            Parsed loop configuration in internal format
        """
        if not loop_config:
            raise ValueError("Loop configuration is required for loop execution")

        # Check if this is the new schema format and validate before conversion
        if "iteration_behavior" in loop_config:
            self._validate_new_schema_raw(loop_config)
            parsed = self._convert_new_schema_config(loop_config)
        elif "loop_behavior" in loop_config:
            # Handle old user-friendly format
            parsed = self._convert_user_friendly_config(loop_config)
        else:
            # Handle legacy format
            parsed = self._parse_legacy_config(loop_config)

        self.logger.debug(f"Parsed loop config: {parsed}")
        return parsed

    def _convert_user_friendly_config(self, user_config: dict) -> dict:
        """
        Convert user-friendly configuration to internal format.

        Args:
            user_config: User-friendly loop configuration

        Returns:
            Internal loop configuration format
        """
        # Convert loop behavior
        loop_type = (
            "context_independent"
            if user_config["loop_behavior"] == "independent"
            else "context_preserving"
        )

        # Convert result handling to aggregation config
        result_handling = user_config["result_handling"]
        aggregation_config = self._convert_result_handling(result_handling)

        # Convert data source to iteration source
        iteration_source = self._convert_data_source(user_config["data_source"])

        # Convert performance settings to concurrency config
        concurrency_config = self._convert_performance_settings(
            user_config.get("performance_settings", {})
        )

        # Convert stop conditions to termination conditions
        termination_conditions = self._convert_stop_conditions(
            user_config.get("stop_conditions", {})
        )

        # Convert error handling to retry config
        retry_config = self._convert_error_handling(
            user_config.get("error_handling", {})
        )

        # Build internal configuration
        internal_config = {
            "loop_type": loop_type,
            "aggregation_config": aggregation_config,
            "iteration_source": iteration_source,
            "loop_body_transitions": user_config["loop_body_transitions"],
        }

        # Add optional configurations
        if concurrency_config:
            internal_config["concurrency"] = concurrency_config

        if termination_conditions:
            internal_config["termination_conditions"] = termination_conditions

        if retry_config:
            internal_config["retry_config"] = retry_config

        return internal_config

    def _convert_new_schema_config(self, new_config: dict) -> dict:
        """
        Convert new schema configuration to internal format.

        Args:
            new_config: New schema loop configuration

        Returns:
            Internal loop configuration format
        """
        # Convert iteration_behavior to loop_type
        loop_type = (
            "context_independent"
            if new_config["iteration_behavior"] == "independent"
            else "context_preserving"
        )

        # Convert iteration_source
        iteration_source = new_config["iteration_source"]

        # Check which type of iteration source we have
        if "iteration_list" in iteration_source:
            # Static list iteration
            iteration_list = iteration_source["iteration_list"]
            batch_size = iteration_source.get("batch_size", 1)

            self.logger.debug(f"📋 Detected iteration_list with {len(iteration_list)} items, batch_size: {batch_size}")
            internal_iteration_source = {
                "type": "list",
                "data": iteration_list,
                "batch_size": batch_size,
            }

        elif "number_range" in iteration_source:
            # Number range iteration
            number_range = iteration_source["number_range"]
            step = iteration_source.get("step", 1)
            batch_size = iteration_source.get("batch_size", 1)

            start = number_range["start"]
            end = number_range["end"]

            self.logger.debug(f"🔢 Detected number_range from {start} to {end} (step: {step}), batch_size: {batch_size}")
            internal_iteration_source = {
                "type": "range",
                "data": {
                    "start": start,
                    "stop": end + 1,  # +1 because range is exclusive but schema end is inclusive
                    "step": step,
                },
                "batch_size": batch_size,
            }

        else:
            raise ValueError("iteration_source must have either 'iteration_list' or 'number_range'")

        # Convert result_aggregation to aggregation_config
        result_aggregation = new_config.get("result_aggregation", {})
        aggregation_type = result_aggregation.get("aggregation_type", "collect_all")

        aggregation_mapping = {
            "collect_all": {"type": "list"},
            "collect_successful": {"type": "list", "filter_successful": True},
            "count_only": {"type": "count"},
            "latest_only": {
                "type": "custom",
                "custom_function": {
                    "type": "lambda",
                    "expression": "results[-1] if results else None",
                },
            },
            "first_success": {
                "type": "custom",
                "custom_function": {
                    "type": "lambda",
                    "expression": "next((r for r in results if r.get('status') == 'success'), None)",
                },
            },
            "combine_text": {"type": "concatenate"},
            # Phase 3: Advanced Aggregation Strategies
            "statistical_summary": {"type": "statistical_summary"},
            "conditional_collect": {"type": "conditional_collect"},
            "hierarchical_merge": {"type": "hierarchical_merge"},
            "time_series": {"type": "time_series"},
            "weighted_average": {"type": "weighted_average"},
            "best_result": {"type": "best_result"},
            "consensus": {"type": "consensus"},
            "streaming_reduce": {"type": "streaming_reduce"},
        }
        aggregation_config = aggregation_mapping.get(aggregation_type, {"type": "list"})

        # Add metadata inclusion if specified
        if result_aggregation.get("include_metadata", False):
            aggregation_config["include_metadata"] = True

        # Convert iteration_settings to concurrency config
        iteration_settings = new_config.get("iteration_settings", {})
        concurrency_config = {
            "enabled": iteration_settings.get("parallel_execution", False),
            "max_concurrent": iteration_settings.get("max_concurrent", 3),
            "preserve_order": iteration_settings.get("preserve_order", True),
            "iteration_timeout": iteration_settings.get("iteration_timeout", 30),
        }

        # Convert error_handling to retry config
        error_handling = new_config.get("error_handling", {})
        on_error = error_handling.get("on_iteration_error", "continue")

        retry_mapping = {
            "continue": {"max_retries": 0},
            "retry_once": {"max_retries": 1, "retry_delay": 1.0},
            "retry_twice": {"max_retries": 2, "retry_delay": 1.0},
            "exit_loop": {"max_retries": 0, "stop_on_error": True},
        }
        retry_config = retry_mapping.get(on_error, {"max_retries": 0})

        # Build internal configuration
        internal_config = {
            "loop_type": loop_type,
            "aggregation_config": aggregation_config,
            "result_aggregation": result_aggregation,  # Keep original for backward compatibility
            "iteration_source": internal_iteration_source,
            "exit_condition": new_config["exit_condition"],
            "concurrency": concurrency_config,
            "retry_config": retry_config,
            "iteration_behavior": new_config["iteration_behavior"],  # Keep for validation
            # Keep original sections for backward compatibility
            "iteration_settings": iteration_settings,
            "error_handling": error_handling,
        }

        # Also preserve the original iteration_source format for tests
        internal_config["iteration_source"]["original"] = iteration_source

        # Preserve loop_body_transitions if present (added by transition handler auto-detection)
        if "loop_body_transitions" in new_config:
            internal_config["loop_body_transitions"] = new_config["loop_body_transitions"]

        return internal_config

    def _parse_legacy_config(self, loop_config: dict) -> dict:
        """
        Parse legacy loop configuration format.

        Args:
            loop_config: Legacy loop configuration

        Returns:
            Parsed and validated loop configuration
        """
        parsed = {
            "loop_type": loop_config.get("loop_type"),
            "aggregation_config": loop_config.get(
                "aggregation_config", {"type": "list"}
            ),
            "iteration_source": loop_config.get("iteration_source", {}),
            "loop_body_transitions": loop_config.get("loop_body_transitions", []),
            "exit_transition": loop_config.get("exit_transition"),
            "concurrency": loop_config.get("concurrency", {"enabled": False}),
        }
        return parsed

    def _convert_result_handling(self, result_handling: str) -> dict:
        """Convert result_handling enum to aggregation_config."""
        mapping = {
            "collect_all": {"type": "list"},
            "collect_successful": {"type": "list", "filter_successful": True},
            "count_only": {"type": "count"},
            "latest_only": {
                "type": "custom",
                "custom_function": {
                    "type": "lambda",
                    "expression": "results[-1] if results else None",
                },
            },
            "combine_text": {"type": "concatenate"},
            "no_collection": {"type": "list", "collect": False},
        }
        return mapping.get(result_handling, {"type": "list"})

    def _convert_data_source(self, data_source: dict) -> dict:
        """Convert data_source to iteration_source."""
        source_type = data_source["source_type"]

        if source_type == "static_list":
            return {"type": "list", "data": data_source.get("static_items", [])}
        elif source_type == "number_range":
            range_config = data_source.get("range_config", {})
            return {
                "type": "range",
                "data": {
                    "start": range_config.get("start", 1),
                    "stop": range_config.get("end", 10),
                    "step": range_config.get("step", 1),
                },
            }
        elif source_type in ["input_data", "previous_result"]:
            return {
                "type": "dynamic",
                "source": source_type,
                "field": data_source.get("input_field", "data"),
            }
        else:
            raise ValueError(f"Unsupported data source type: {source_type}")

    def _convert_performance_settings(self, perf_settings: dict) -> dict:
        """Convert performance_settings to concurrency config."""
        if not perf_settings.get("run_in_parallel", False):
            return {"enabled": False}

        return {
            "enabled": True,
            "max_concurrent": perf_settings.get("max_parallel_items", 3),
            "preserve_order": perf_settings.get("keep_order", True),
            "iteration_timeout": perf_settings.get("timeout_per_item", 30),
        }

    def _convert_stop_conditions(self, stop_conditions: dict) -> dict:
        """Convert stop_conditions to termination_conditions."""
        if not stop_conditions:
            return {}

        termination = {}

        if stop_conditions.get("max_items", 0) > 0:
            termination["max_iterations"] = stop_conditions["max_items"]

        if stop_conditions.get("max_time_minutes", 0) > 0:
            termination["timeout_seconds"] = stop_conditions["max_time_minutes"] * 60

        if stop_conditions.get("stop_on_first_success", False):
            termination["success_condition"] = "result.get('status') == 'success'"

        if stop_conditions.get("stop_after_failures", 0) > 0:
            termination["failure_threshold"] = stop_conditions["stop_after_failures"]

        return termination

    def _convert_error_handling(self, error_handling: dict) -> dict:
        """Convert error_handling to retry_config."""
        if not error_handling:
            return {}

        on_error = error_handling.get("on_item_error", "continue")

        retry_mapping = {
            "continue": {"max_retries": 0},
            "retry_once": {"max_retries": 1, "retry_delay": 1.0},
            "retry_twice": {"max_retries": 2, "retry_delay": 1.0},
            "stop_loop": {"max_retries": 0, "stop_on_error": True},
        }

        return retry_mapping.get(on_error, {"max_retries": 0})

    def prepare_iteration_data_from_input(self) -> None:
        """
        Prepare iteration data from input data based on the new schema.
        """
        iteration_source = self.current_loop_config["iteration_source"]
        source_type = iteration_source["type"]
        batch_size = iteration_source.get("batch_size", 1)

        if source_type == "list":
            # Extract items from static list
            data = iteration_source.get("data", [])
            if not isinstance(data, list):
                raise ValueError("List iteration source data must be an array")

            # Apply batching if batch_size > 1
            if batch_size > 1:
                self.current_iteration_data = self._create_batched_data(data, batch_size)
                self.logger.info(f"📦 Created {len(self.current_iteration_data)} batches of size {batch_size} from {len(data)} items")
            else:
                self.current_iteration_data = [(i, item) for i, item in enumerate(data)]

        elif source_type == "range":
            # Generate range values
            range_config = iteration_source.get("data", {})
            start = range_config.get("start", 0)
            stop = range_config.get("stop", 0)
            step = range_config.get("step", 1)
            range_data = list(range(start, stop, step))

            # Apply batching if batch_size > 1
            if batch_size > 1:
                self.current_iteration_data = self._create_batched_data(range_data, batch_size)
                self.logger.info(f"📦 Created {len(self.current_iteration_data)} batches of size {batch_size} from range {start}-{stop-1}")
            else:
                self.current_iteration_data = [(i, value) for i, value in enumerate(range_data)]

        elif source_type == "dynamic":
            # Try handle-based resolution first
            iteration_data = self._extract_iteration_data_from_handles()

            if iteration_data is not None:
                self.logger.info(f"✅ Successfully extracted iteration data from handle mappings: {len(iteration_data)} items")

                # Apply batching if batch_size > 1
                if batch_size > 1:
                    self.current_iteration_data = self._create_batched_data(iteration_data, batch_size)
                    self.logger.info(f"📦 Created {len(self.current_iteration_data)} batches of size {batch_size}")
                else:
                    self.current_iteration_data = [(i, item) for i, item in enumerate(iteration_data)]
                return

            # Fall back to field path navigation if no handle mappings
            self.logger.debug("No handle mappings found or failed, falling back to field path navigation")
            self._extract_iteration_data_from_field_path(iteration_source, batch_size)

        else:
            raise ValueError(f"Unsupported iteration source type: {source_type}")

        self.logger.info(
            f"Prepared {len(self.current_iteration_data)} iterations from {source_type} source"
        )

    def _extract_iteration_data_from_handles(self) -> Optional[List[Any]]:
        """
        Extract iteration data using handle mappings, similar to how other nodes work.

        Returns:
            List of items to iterate over, or None if no suitable handle mapping found
        """
        if not self.input_data_configs:
            self.logger.debug("No input_data_configs available for handle mapping")
            return None

        # Look for input data configs that might contain array data
        for input_config in self.input_data_configs:
            if not isinstance(input_config, dict):
                continue

            # Check if this input config has a handle that resolves to array data
            handle_id = input_config.get("handle_id")
            if not handle_id:
                continue

            # Get the resolved data for this handle from input_data
            resolved_data = self.input_data.get(handle_id)
            if resolved_data is None:
                continue

            # Check if the resolved data is a list or contains a list
            if isinstance(resolved_data, list):
                self.logger.debug(f"Found array data in handle '{handle_id}': {len(resolved_data)} items")
                return resolved_data
            elif isinstance(resolved_data, dict):
                # Look for common array field names in the resolved data
                for field_name in ['data', 'items', 'list', 'array', 'results']:
                    if field_name in resolved_data and isinstance(resolved_data[field_name], list):
                        self.logger.debug(f"Found array data in handle '{handle_id}.{field_name}': {len(resolved_data[field_name])} items")
                        return resolved_data[field_name]

        self.logger.debug("No suitable array data found in handle mappings")
        return None

    def _create_batched_data(self, data: List[Any], batch_size: int) -> List[Tuple[int, List[Any]]]:
        """
        Create batched iteration data from a list of items.

        Args:
            data: List of items to batch
            batch_size: Size of each batch

        Returns:
            List of tuples (batch_index, batch_items)
        """
        batched_data = []
        for i in range(0, len(data), batch_size):
            batch = data[i:i + batch_size]
            batch_index = i // batch_size
            batched_data.append((batch_index, batch))

        self.logger.debug(f"📦 Created {len(batched_data)} batches from {len(data)} items (batch_size={batch_size})")
        return batched_data

    def _extract_iteration_data_from_field_path(self, iteration_source: dict, batch_size: int = 1) -> None:
        """
        Extract iteration data using field path navigation (fallback method).

        Args:
            iteration_source: The iteration source configuration
        """
        field_path = iteration_source.get("field", "data")
        input_data = self.input_data

        # Log debug information for troubleshooting
        self.logger.debug(f"Attempting to extract iteration data from field path: '{field_path}'")
        self.logger.debug(f"Available input data keys: {list(input_data.keys()) if isinstance(input_data, dict) else 'Not a dict'}")
        self.logger.debug(f"Input data structure: {input_data}")

        # Navigate to the field using dot notation
        try:
            data = input_data
            for field in field_path.split("."):
                data = data[field]

            if not isinstance(data, list):
                raise ValueError(
                    f"Input field '{field_path}' must contain an array, but found: {type(data).__name__}"
                )

            # Apply batching if batch_size > 1
            if batch_size > 1:
                self.current_iteration_data = self._create_batched_data(data, batch_size)
                self.logger.info(f"📦 Created {len(self.current_iteration_data)} batches of size {batch_size} from field '{field_path}'")
            else:
                self.current_iteration_data = [(i, item) for i, item in enumerate(data)]

        except (KeyError, TypeError) as e:
            # Provide more detailed error information
            available_keys = list(input_data.keys()) if isinstance(input_data, dict) else "N/A (not a dict)"

            # Try some common fallback patterns
            fallback_data = None
            if isinstance(input_data, dict):
                # Try common field names that might contain array data
                for fallback_field in ['data', 'items', 'list', 'array', 'results']:
                    if fallback_field in input_data and isinstance(input_data[fallback_field], list):
                        self.logger.warning(
                            f"Field '{field_path}' not found, using fallback field '{fallback_field}' "
                            f"which contains {len(input_data[fallback_field])} items"
                        )
                        fallback_data = input_data[fallback_field]
                        break

                # If no fallback found, check if the entire input_data is a list
                if fallback_data is None and isinstance(input_data, list):
                    self.logger.warning(
                        f"Field '{field_path}' not found, using entire input data as array "
                        f"which contains {len(input_data)} items"
                    )
                    fallback_data = input_data

            if fallback_data is not None:
                # Apply batching if batch_size > 1
                if batch_size > 1:
                    self.current_iteration_data = self._create_batched_data(fallback_data, batch_size)
                    self.logger.info(f"📦 Created {len(self.current_iteration_data)} batches of size {batch_size} from fallback data")
                else:
                    self.current_iteration_data = [(i, item) for i, item in enumerate(fallback_data)]
                return

            # If no fallback worked, raise the original error with detailed information
            raise ValueError(
                f"Could not extract iteration data from input field '{field_path}': {str(e)}. "
                f"Available input data keys: {available_keys}. "
                f"Input data: {input_data}"
            )

    async def execute_loop_with_outputs(self) -> Dict[str, Any]:
        """
        Execute the loop with dual output handling (iteration and exit outputs).

        Returns:
            Dictionary containing both iteration outputs and final exit output
        """
        if not self.current_iteration_data:
            self.logger.warning("No iteration data available, returning empty result")
            return {
                "iteration_outputs": [],
                "exit_output": {
                    "aggregated_results": [],
                    "metadata": {"total_iterations": 0},
                },
            }

        # Initialize output tracking
        iteration_outputs = []
        self.iteration_results = {}
        self.loop_context["total_iterations"] = len(self.current_iteration_data)

        # Execute the actual loop logic to populate iteration_results
        # Check if concurrency is enabled
        iteration_settings = self.current_loop_config.get("iteration_settings", {})
        if iteration_settings.get("parallel_execution", False):
            await self.execute_concurrent_loop()
        else:
            await self.execute_sequential_loop()

        # Create iteration outputs from the executed results
        for iteration_index, iteration_item in self.current_iteration_data:
            try:
                # Create iteration output
                iteration_output = await self.create_iteration_output(
                    iteration_index, iteration_item
                )
                iteration_outputs.append(iteration_output)

                # Check exit conditions after each iteration
                if await self.should_exit_loop(iteration_index):
                    self.logger.info(
                        f"🛑 Loop exit condition met at iteration {iteration_index}"
                    )
                    break

            except Exception as e:
                await self.handle_iteration_error(e, iteration_index, iteration_item)

        # Create final exit output
        exit_output = await self.create_exit_output()

        # Create result with both the structured output and handle-accessible data
        result = {
            "iteration_outputs": iteration_outputs,
            "exit_output": exit_output,
            # Add handle-accessible data for downstream nodes
            "final_results": exit_output.get("aggregated_results", []),
            "current_item": iteration_outputs[-1].get("current_item") if iteration_outputs else None,
        }

        self.logger.debug(
            f"🔍 Loop executor final result - iteration_results: {self.iteration_results}"
        )
        self.logger.debug(
            f"🔍 Loop executor final result - aggregated_results: {exit_output.get('aggregated_results', [])}"
        )
        self.logger.debug(
            f"🔍 Loop executor final result - final_results: {result.get('final_results', [])}"
        )

        return result

    async def create_iteration_output(
        self, iteration_index: int, iteration_item: Any
    ) -> Dict[str, Any]:
        """
        Create output data for a single iteration to be sent to connected nodes.

        Args:
            iteration_index: Index of the current iteration
            iteration_item: Data item for this iteration

        Returns:
            Dictionary containing iteration output data
        """
        return {
            "current_item": iteration_item,
            "iteration_index": iteration_index,
            "iteration_metadata": {
                "timestamp": asyncio.get_event_loop().time(),
                "loop_id": (
                    self.loop_state_manager.loop_id if self.loop_state_manager else None
                ),
                "total_iterations": len(self.current_iteration_data),
            },
        }

    async def create_exit_output(self) -> Dict[str, Any]:
        """
        Create final exit output with aggregated results.

        Returns:
            Dictionary containing aggregated results and metadata
        """
        # Use aggregator for result processing
        if hasattr(self, "aggregator") and self.aggregator:
            # Use iteration_results directly as it's already a Dict[int, Any]
            aggregation_config = {"type": "list"}
            if hasattr(self, "current_loop_config") and self.current_loop_config:
                aggregation_config = self.current_loop_config.get(
                    "aggregation_config", {"type": "list"}
                )

            aggregated_results = self.aggregator.aggregate_results(
                self.iteration_results,
                aggregation_config,
            )
        else:
            # Fallback aggregation
            aggregated_results = [
                self.iteration_results.get(i, {})
                for i in range(len(self.current_iteration_data))
            ]

        return {
            "aggregated_results": aggregated_results,
            "metadata": {
                "total_iterations": len(self.current_iteration_data),
                "completed_iterations": self.loop_context.get(
                    "completed_iterations", 0
                ),
                "failed_iterations": self.loop_context.get("failed_iterations", 0),
                "loop_id": (
                    self.loop_state_manager.loop_id if self.loop_state_manager else None
                ),
                "execution_time": asyncio.get_event_loop().time()
                - self.loop_context.get("start_time", 0),
            },
        }

    async def should_exit_loop(self, current_iteration: int) -> bool:
        """
        Check if loop should exit based on exit conditions using the exit condition evaluator.

        Args:
            current_iteration: Current iteration index

        Returns:
            True if loop should exit, False otherwise
        """
        try:
            # Use the exit condition evaluator for consistent evaluation
            should_exit = self.exit_condition_evaluator.should_exit_loop(
                current_iteration=current_iteration,
                total_iterations=len(self.current_iteration_data),
                iteration_results=self.iteration_results,
                loop_metadata=self.loop_context,
            )

            # Update loop state manager with exit condition evaluation
            if self.loop_state_manager:
                if should_exit:
                    self.loop_state_manager.set_exit_condition_state("met")
                else:
                    self.loop_state_manager.set_exit_condition_state("evaluating")

                # Add evaluation result to state
                evaluation_result = {
                    "iteration_index": current_iteration,
                    "should_exit": should_exit,
                    "evaluation_summary": self.exit_condition_evaluator.get_evaluation_summary(),
                }
                self.loop_state_manager.add_exit_condition_result(
                    current_iteration, evaluation_result
                )

            return should_exit

        except Exception as e:
            self.logger.error(f"Error evaluating exit condition: {str(e)}")
            # Default to continuing the loop on error
            return False

    def validate_loop_config(self, config: dict) -> None:
        """
        Validate loop configuration parameters for both new and legacy formats.

        Args:
            config: Parsed loop configuration

        Raises:
            ValueError: If configuration is invalid
        """
        # Check if this is new schema format (has iteration_behavior) or legacy format
        if "iteration_behavior" in config:
            # New schema was already validated before conversion
            pass
        else:
            self._validate_legacy_config(config)

        self.logger.debug("Loop configuration validation passed")

    def _validate_new_schema_raw(self, config: dict) -> None:
        """Validate new schema loop configuration."""
        # Validate required fields for new schema
        required_fields = ["iteration_behavior", "iteration_source", "exit_condition"]
        for field in required_fields:
            if not config.get(field):
                raise ValueError(f"Required loop configuration field missing: {field}")

        # Validate iteration_behavior
        valid_behaviors = ["independent", "sequential"]
        if config["iteration_behavior"] not in valid_behaviors:
            raise ValueError(
                f"Invalid iteration_behavior: {config['iteration_behavior']}. Must be one of: {valid_behaviors}"
            )

        # Validate iteration_source
        iteration_source = config["iteration_source"]

        # Check that exactly one of iteration_list or number_range is present
        has_iteration_list = "iteration_list" in iteration_source
        has_number_range = "number_range" in iteration_source

        if not has_iteration_list and not has_number_range:
            raise ValueError("iteration_source must have either 'iteration_list' or 'number_range'")

        if has_iteration_list and has_number_range:
            raise ValueError("iteration_source cannot have both 'iteration_list' and 'number_range'")

        if has_iteration_list:
            # Validate iteration_list
            iteration_list = iteration_source["iteration_list"]
            if not isinstance(iteration_list, list):
                raise ValueError("iteration_list must be an array")

            # Validate optional batch_size
            batch_size = iteration_source.get("batch_size", 1)
            if not isinstance(batch_size, int) or batch_size <= 0:
                raise ValueError("batch_size must be a positive integer")

        elif has_number_range:
            # Validate number_range
            number_range = iteration_source["number_range"]
            if not isinstance(number_range, dict):
                raise ValueError("number_range must be an object")

            if "start" not in number_range or "end" not in number_range:
                raise ValueError("number_range must have 'start' and 'end' fields")

            start = number_range["start"]
            end = number_range["end"]
            if not isinstance(start, int) or not isinstance(end, int):
                raise ValueError("number_range start and end must be integers")

            if start > end:
                raise ValueError("number_range start must be less than or equal to end")

            # Validate optional step
            step = iteration_source.get("step", 1)
            if not isinstance(step, int) or step <= 0:
                raise ValueError("step must be a positive integer")

            # Validate optional batch_size
            batch_size = iteration_source.get("batch_size", 1)
            if not isinstance(batch_size, int) or batch_size <= 0:
                raise ValueError("batch_size must be a positive integer")

        # Validate exit_condition
        exit_condition = config["exit_condition"]
        if not exit_condition.get("condition_type"):
            raise ValueError("exit_condition.condition_type is required")

        valid_condition_types = [
            "all_items_processed",
            "max_iterations",
            "timeout",
            "success_condition",
            "failure_threshold",
        ]
        if exit_condition["condition_type"] not in valid_condition_types:
            raise ValueError(
                f"Invalid exit_condition.condition_type: {exit_condition['condition_type']}. Must be one of: {valid_condition_types}"
            )

    def _validate_legacy_config(self, config: dict) -> None:
        """Validate legacy loop configuration."""
        # Validate required fields for legacy format
        required_fields = [
            "loop_type",
            "aggregation_config",
            "iteration_source",
        ]
        for field in required_fields:
            if not config.get(field):
                raise ValueError(f"Required loop configuration field missing: {field}")

        # Validate enum values
        valid_loop_types = ["context_preserving", "context_independent"]
        if config["loop_type"] not in valid_loop_types:
            raise ValueError(
                f"Invalid loop_type: {config['loop_type']}. Must be one of: {valid_loop_types}"
            )

        # Validate aggregation config
        if not self.aggregator.validate_aggregation_config(
            config["aggregation_config"]
        ):
            raise ValueError("Invalid aggregation configuration")

        # Validate iteration source
        iteration_source = config["iteration_source"]
        if not iteration_source.get("type"):
            raise ValueError("iteration_source.type is required")

        valid_source_types = ["list", "range", "condition", "dynamic"]
        if iteration_source["type"] not in valid_source_types:
            raise ValueError(
                f"Invalid iteration_source.type: {iteration_source['type']}. Must be one of: {valid_source_types}"
            )

    def prepare_iteration_data(self) -> None:
        """
        Prepare iteration data based on the iteration source configuration.
        """
        iteration_source = self.current_loop_config["iteration_source"]
        source_type = iteration_source["type"]

        if source_type == "list":
            # Extract items from list data
            data = iteration_source.get("data", [])
            if not isinstance(data, list):
                raise ValueError("List iteration source data must be an array")
            self.current_iteration_data = [(i, item) for i, item in enumerate(data)]

        elif source_type == "range":
            # Generate range values
            range_config = iteration_source.get("data", {})
            start = range_config.get("start", 0)
            stop = range_config.get("stop", 0)
            step = range_config.get("step", 1)
            self.current_iteration_data = [
                (i, value) for i, value in enumerate(range(start, stop, step))
            ]

        elif source_type == "condition":
            # For condition-based loops, we'll start with a basic implementation
            # This will be expanded in Phase 5 for advanced features
            condition = iteration_source.get("condition", "")
            self.logger.warning(
                f"Condition-based loops not fully implemented yet. Condition: {condition}"
            )
            self.current_iteration_data = []  # Empty for now

        self.logger.info(
            f"Prepared {len(self.current_iteration_data)} iterations for execution"
        )

    async def initialize_loop_state(self, transition_id: str) -> None:
        """
        Initialize loop execution state.

        Args:
            transition_id: ID of the loop transition
        """
        # Generate unique loop ID
        loop_id = f"loop_{transition_id}_{uuid.uuid4().hex[:8]}"

        # Create LoopStateManager instance
        self.loop_state_manager = LoopStateManager(
            loop_id=loop_id,
            transition_id=transition_id,
            workflow_id=self.state_manager.workflow_id,
        )

        # Initialize loop state
        total_iterations = len(self.current_iteration_data)
        self.loop_state_manager.initialize_loop_state(
            total_iterations=total_iterations,
            metadata={
                "loop_config": self.current_loop_config,
                "start_time": asyncio.get_event_loop().time(),
            },
        )

        # Store loop state in workflow state manager
        loop_state_data = self.loop_state_manager.backup_loop_state()
        self.state_manager.store_loop_state(loop_id, transition_id, loop_state_data)

        # Initialize exit condition evaluator
        exit_condition_config = self.current_loop_config.get("exit_condition", {})
        self.exit_condition_evaluator.initialize_evaluation(exit_condition_config)
        self.loop_state_manager.set_exit_condition_config(exit_condition_config)

        # Configure error handler for new schema
        error_handling_config = self.current_loop_config.get("error_handling", {})
        self.error_handler.configure_error_handling(error_handling_config)

        # Phase 3: Configure advanced error handling features
        advanced_error_config = error_handling_config.get("advanced_features", {})
        if advanced_error_config:
            self.error_handler.configure_advanced_error_handling(advanced_error_config)

        # Initialize local state for backward compatibility
        self.iteration_results = {}
        self.loop_context = {
            "transition_id": transition_id,
            "loop_id": loop_id,
            "total_iterations": total_iterations,
            "completed_iterations": 0,
            "failed_iterations": 0,
        }

        self.logger.debug(
            f"Loop state initialized for transition: {transition_id}, loop_id: {loop_id}"
        )

    async def execute_loop(self) -> List[Union[Dict[str, Any], str]]:
        """
        Execute the main loop logic.

        Returns:
            Aggregated loop results
        """
        if not self.current_iteration_data:
            self.logger.warning("No iteration data available, returning empty result")
            return []

        self.loop_context["total_iterations"] = len(self.current_iteration_data)

        # Check if concurrency is enabled
        concurrency_config = self.current_loop_config.get("concurrency", {})
        if concurrency_config.get("enabled", False):
            await self.execute_concurrent_loop()
        else:
            await self.execute_sequential_loop()

        # Use aggregator for result processing
        return self.aggregate_results()

    async def execute_sequential_loop(self) -> None:
        """Execute loop iterations sequentially."""
        self.logger.info("Executing loop sequentially")

        for iteration_index, iteration_item in self.current_iteration_data:
            try:
                result = await self.execute_single_iteration(
                    iteration_index, iteration_item
                )
                self.iteration_results[iteration_index] = result
                self.loop_context["completed_iterations"] += 1

            except Exception as e:
                await self.handle_iteration_error(e, iteration_index, iteration_item)
                self.loop_context["failed_iterations"] += 1

    async def execute_concurrent_loop(self) -> None:
        """Execute loop iterations concurrently with enhanced features."""
        concurrency_config = self.current_loop_config["concurrency"]
        max_concurrent = concurrency_config.get("max_concurrent", 5)
        preserve_order = concurrency_config.get("preserve_order", True)
        early_exit_enabled = concurrency_config.get("early_exit", False)
        progress_tracking = concurrency_config.get("progress_tracking", True)

        # Phase 3 Advanced Features
        adaptive_concurrency = concurrency_config.get("adaptive_concurrency", False)
        load_balancing = concurrency_config.get("load_balancing", "round_robin")
        circuit_breaker = concurrency_config.get("circuit_breaker", {})
        batch_processing = concurrency_config.get("batch_processing", {})

        self.logger.info(
            f"🚀 Executing loop concurrently: max_concurrent={max_concurrent}, "
            f"preserve_order={preserve_order}, early_exit={early_exit_enabled}, "
            f"adaptive={adaptive_concurrency}, load_balancing={load_balancing}"
        )

        # Initialize concurrent execution state with Phase 3 enhancements
        self.concurrent_state = {
            "active_tasks": {},
            "completed_count": 0,
            "failed_count": 0,
            "cancelled_count": 0,
            "early_exit_triggered": False,
            "start_time": asyncio.get_event_loop().time(),
            # Phase 3 Advanced Features
            "adaptive_metrics": {
                "avg_execution_time": 0.0,
                "success_rate": 1.0,
                "resource_utilization": 0.0,
                "throughput": 0.0,
                "last_adjustment": 0.0
            },
            "circuit_breaker_state": {
                "state": "closed",  # closed, open, half_open
                "failure_count": 0,
                "last_failure_time": 0.0,
                "success_count": 0
            },
            "load_balancer": {
                "worker_pools": [],
                "current_pool_index": 0,
                "pool_metrics": {}
            },
            "batch_state": {
                "current_batch": 0,
                "batch_results": [],
                "batch_start_time": 0.0
            }
        }

        # Create semaphore to limit concurrent executions
        semaphore = asyncio.Semaphore(max_concurrent)

        # Create cancellation event for early exit
        cancellation_event = asyncio.Event()

        # Initialize adaptive concurrency controller
        if adaptive_concurrency:
            await self._initialize_adaptive_concurrency(concurrency_config)

        # Initialize circuit breaker if configured
        if circuit_breaker.get("enabled", False):
            await self._initialize_circuit_breaker(circuit_breaker)

        async def execute_with_enhanced_control(iteration_index, iteration_item):
            """Enhanced execution wrapper with progress tracking, circuit breaker, and adaptive features."""
            task_id = f"iter_{iteration_index}"
            execution_start_time = asyncio.get_event_loop().time()

            # Circuit breaker check
            if circuit_breaker.get("enabled", False):
                if not await self._check_circuit_breaker():
                    self.concurrent_state["cancelled_count"] += 1
                    return iteration_index, None, "circuit_breaker_open"

            async with semaphore:
                # Check for early exit before starting
                if early_exit_enabled and cancellation_event.is_set():
                    self.concurrent_state["cancelled_count"] += 1
                    return iteration_index, None, "cancelled"

                # Track active task with enhanced metrics
                self.concurrent_state["active_tasks"][task_id] = {
                    "iteration_index": iteration_index,
                    "start_time": execution_start_time,
                    "status": "running",
                    "worker_pool": await self._select_worker_pool(load_balancing) if load_balancing != "round_robin" else None,
                    "resource_allocation": await self._allocate_resources(iteration_index, iteration_item)
                }

                try:
                    # Execute iteration with timeout if configured
                    timeout = concurrency_config.get("iteration_timeout")
                    if timeout:
                        result = await asyncio.wait_for(
                            self.execute_single_iteration(
                                iteration_index, iteration_item
                            ),
                            timeout=timeout,
                        )
                    else:
                        result = await self.execute_single_iteration(
                            iteration_index, iteration_item
                        )

                    # Store result with proper indexing
                    self.iteration_results[iteration_index] = result
                    self.concurrent_state["completed_count"] += 1

                    # Update adaptive metrics
                    execution_time = asyncio.get_event_loop().time() - execution_start_time
                    await self._update_adaptive_metrics(execution_time, True, iteration_index)

                    # Update circuit breaker on success
                    if circuit_breaker.get("enabled", False):
                        await self._record_circuit_breaker_success()

                    # Update progress
                    if progress_tracking:
                        await self._update_concurrent_progress()

                    # Check for early exit conditions
                    if early_exit_enabled:
                        should_exit = await self._check_early_exit_conditions(
                            result, iteration_index
                        )
                        if should_exit:
                            self.concurrent_state["early_exit_triggered"] = True
                            cancellation_event.set()
                            self.logger.info(
                                f"🛑 Early exit triggered at iteration {iteration_index}"
                            )

                    return iteration_index, result, "completed"

                except asyncio.TimeoutError:
                    self.logger.warning(
                        f"⏰ Iteration {iteration_index} timed out after {timeout}s"
                    )
                    await self.handle_iteration_error(
                        TimeoutError(f"Iteration timeout after {timeout}s"),
                        iteration_index,
                        iteration_item,
                    )
                    self.concurrent_state["failed_count"] += 1
                    return iteration_index, None, "timeout"

                except Exception as e:
                    # Update adaptive metrics for failure
                    execution_time = asyncio.get_event_loop().time() - execution_start_time
                    await self._update_adaptive_metrics(execution_time, False, iteration_index)

                    # Update circuit breaker on failure
                    if circuit_breaker.get("enabled", False):
                        await self._record_circuit_breaker_failure(e)

                    await self.handle_iteration_error(
                        e, iteration_index, iteration_item
                    )
                    self.concurrent_state["failed_count"] += 1
                    return iteration_index, None, "failed"

                finally:
                    # Remove from active tasks
                    self.concurrent_state["active_tasks"].pop(task_id, None)

                    # Release allocated resources
                    await self._release_resources(task_id)

        # Create all tasks
        tasks = [
            asyncio.create_task(
                execute_with_enhanced_control(iteration_index, iteration_item),
                name=f"loop_iter_{iteration_index}",
            )
            for iteration_index, iteration_item in self.current_iteration_data
        ]

        # Execute with proper cancellation handling
        try:
            if early_exit_enabled:
                # Monitor for early exit while executing
                results = await self._execute_with_early_exit_monitoring(
                    tasks, cancellation_event
                )
            else:
                # Standard concurrent execution
                results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results and ensure proper ordering if required
            if preserve_order:
                await self._ensure_result_ordering(results)

        except Exception as e:
            self.logger.error(f"❌ Concurrent execution failed: {str(e)}")
            # Cancel remaining tasks
            for task in tasks:
                if not task.done():
                    task.cancel()
            raise

        finally:
            # Log execution statistics
            await self._log_concurrent_execution_stats()

    async def execute_single_iteration(
        self, iteration_index: int, iteration_item: Any
    ) -> Any:
        """
        Execute a single loop iteration with retry logic for transient failures.

        Args:
            iteration_index: Index of the current iteration
            iteration_item: Data item for this iteration

        Returns:
            Result from the iteration execution
        """
        self.logger.debug(
            f"Executing iteration {iteration_index} with item: {type(iteration_item)}"
        )

        # Get retry configuration from loop config
        retry_config = self.current_loop_config.get("retry_config", {})
        max_retries = retry_config.get("max_retries", 3)
        retry_delay = retry_config.get("retry_delay", 1.0)
        exponential_backoff = retry_config.get("exponential_backoff", True)

        # Update loop state manager
        if self.loop_state_manager:
            self.loop_state_manager.set_iteration_status(iteration_index, "running")
            self.loop_state_manager.current_iteration_index = iteration_index

        # Create iteration context
        iteration_context = self.create_iteration_context(
            iteration_index, iteration_item
        )

        # Store iteration context in loop state manager
        if self.loop_state_manager:
            self.loop_state_manager.store_iteration_context(
                iteration_index, iteration_context
            )

        # Execute with retry logic
        last_error = None
        for attempt in range(max_retries + 1):
            try:
                # Execute loop body logic for this iteration
                # Use the loop body chain executor for actual execution
                loop_body_result = await self._execute_loop_body_chain(
                    iteration_index, iteration_item, iteration_context
                )

                # Return the processed result from loop body execution
                result = loop_body_result

                # Store iteration result in loop state manager
                if self.loop_state_manager:
                    self.loop_state_manager.store_iteration_result(
                        iteration_index, result
                    )
                    self.loop_state_manager.set_iteration_status(
                        iteration_index, "completed"
                    )

                    # Update workflow state manager with latest loop state
                    loop_state_data = self.loop_state_manager.backup_loop_state()
                    self.state_manager.store_loop_state(
                        self.loop_state_manager.loop_id,
                        self.loop_state_manager.transition_id,
                        loop_state_data,
                    )

                # Check termination conditions after successful iteration
                termination_result = await self._evaluate_termination_conditions(
                    iteration_index, result
                )
                if termination_result["should_terminate"]:
                    await self._handle_loop_break(
                        termination_result["reason"], iteration_index
                    )
                    # Add termination info to result
                    result["termination_triggered"] = True
                    result["termination_reason"] = termination_result["reason"]
                    result["termination_details"] = termination_result.get("details")

                # Phase 3: Update circuit breaker and recovery success rates on success
                advanced_error_config = self.current_loop_config.get("error_handling", {}).get("advanced_features", {})
                if advanced_error_config:
                    self.error_handler._update_circuit_breaker(True)

                    # Update recovery success rates for any error types that were encountered
                    if hasattr(self, '_current_iteration_errors'):
                        for error_key in self._current_iteration_errors:
                            self.error_handler.update_recovery_success_rate(error_key, True)
                        delattr(self, '_current_iteration_errors')

                self.logger.debug(
                    f"Iteration {iteration_index} completed on attempt {attempt + 1}"
                )
                return result

            except Exception as e:
                last_error = e

                # Phase 3: Use advanced error handling if configured
                advanced_error_config = self.current_loop_config.get("error_handling", {}).get("advanced_features", {})
                if advanced_error_config:
                    # Use advanced error handling
                    error_context = {
                        "iteration_item": iteration_item,
                        "loop_config": self.current_loop_config,
                        "loop_context": self.loop_context,
                        "max_retries": max_retries,
                        "attempt": attempt
                    }

                    recovery_action = await self.error_handler.handle_error_with_advanced_features(
                        e, iteration_index, error_context
                    )

                    # Update circuit breaker
                    self.error_handler._update_circuit_breaker(False)

                    # Track error key for recovery success rate updates
                    error_key = f"{e.__class__.__module__}.{e.__class__.__name__}"
                    if not hasattr(self, '_current_iteration_errors'):
                        self._current_iteration_errors = set()
                    self._current_iteration_errors.add(error_key)

                    # Handle recovery action
                    if recovery_action["action"] == "circuit_open":
                        self.logger.error(f"🚨 Circuit breaker open - stopping iteration {iteration_index}")
                        break
                    elif recovery_action["action"] == "threshold_exceeded":
                        self.logger.error(f"🚨 Failure threshold exceeded - stopping iteration {iteration_index}")
                        break
                    elif recovery_action["action"] in ["adaptive_retry", "retry"]:
                        if attempt < max_retries:
                            delay = recovery_action.get("delay", retry_delay)
                            self.logger.warning(
                                f"Advanced retry for iteration {iteration_index} (attempt {attempt + 1}) in {delay:.2f}s"
                            )

                            # Update state to show retry
                            if self.loop_state_manager:
                                self.loop_state_manager.set_iteration_status(
                                    iteration_index, "retrying"
                                )

                            await asyncio.sleep(delay)
                            continue
                        else:
                            break
                    elif recovery_action["action"] == "skip":
                        self.logger.warning(f"Skipping iteration {iteration_index} due to advanced error handling")
                        break
                    else:
                        # Continue with standard retry logic
                        pass

                # Standard retry logic
                is_retryable = self._is_retryable_error(e)

                if attempt < max_retries and is_retryable:
                    # Calculate delay with exponential backoff
                    current_delay = retry_delay
                    if exponential_backoff:
                        current_delay = retry_delay * (2**attempt)

                    self.logger.warning(
                        f"Iteration {iteration_index} failed on attempt {attempt + 1} with retryable error: {str(e)}. "
                        f"Retrying in {current_delay} seconds..."
                    )

                    # Update state to show retry
                    if self.loop_state_manager:
                        self.loop_state_manager.set_iteration_status(
                            iteration_index, "retrying"
                        )

                    await asyncio.sleep(current_delay)
                else:
                    # Final failure or non-retryable error
                    self.logger.error(
                        f"Iteration {iteration_index} failed permanently after {attempt + 1} attempts: {str(e)}"
                    )
                    break

        # If we get here, all retries failed
        raise last_error if last_error else Exception("Unknown iteration failure")

    async def _execute_loop_body_chain(
        self, iteration_index: int, iteration_item: Any, iteration_context: Dict[str, Any]
    ) -> Any:
        """
        Execute the loop body chain for a single iteration using the chain executor.

        Args:
            iteration_index: Index of the current iteration
            iteration_item: Data item for this iteration
            iteration_context: Context data for this iteration

        Returns:
            Result from executing the loop body chain
        """
        # Get loop body configuration from the current loop config
        loop_body_config = self.current_loop_config.get("loop_body_configuration", {})

        # If no loop body configuration or empty entry/exit transitions, check for loop_body_transitions
        if (not loop_body_config or
            (not loop_body_config.get("entry_transitions") and not loop_body_config.get("exit_transitions"))) and "loop_body_transitions" in self.current_loop_config:
            # Convert loop_body_transitions to entry/exit transitions format
            loop_body_transitions = self.current_loop_config.get("loop_body_transitions", [])
            if not loop_body_config:
                loop_body_config = {}
            loop_body_config.update({
                "entry_transitions": loop_body_transitions[:1] if loop_body_transitions else [],
                "exit_transitions": loop_body_transitions[-1:] if loop_body_transitions else [],
                "chain_completion_detection": "explicit_exit_transitions"
            })
            self.logger.debug(f"🔄 Converted loop_body_transitions to entry/exit format: {loop_body_transitions}")

        if not loop_body_config.get("entry_transitions") and not loop_body_config.get("exit_transitions"):
            self.logger.warning("No loop body configuration found, returning iteration item")
            return iteration_item

        # Get the current transition ID for context
        current_transition_id = getattr(self, '_current_transition_id', 'unknown')

        try:
            # Execute the loop body chain using the chain executor
            result = await self.loop_body_chain_executor.execute_loop_body_chain(
                loop_transition_id=current_transition_id,
                iteration_index=iteration_index,
                iteration_item=iteration_item,
                iteration_context=iteration_context,
                loop_body_config=loop_body_config
            )

            self.logger.debug(
                f"✅ Loop body chain executed for iteration {iteration_index}, result: {result}"
            )

            return result

        except Exception as e:
            self.logger.error(
                f"❌ Loop body chain execution failed for iteration {iteration_index}: {str(e)}"
            )
            # Depending on error handling configuration, we might want to continue or fail
            error_handling = self.current_loop_config.get("error_handling", {})
            if error_handling.get("on_iteration_error", "continue") == "continue":
                # Return the original item if we're configured to continue on errors
                return iteration_item
            else:
                raise

    def notify_transition_completion(self, transition_id: str, result: Any) -> None:
        """
        Notify the loop executor that a transition has completed.

        This method should be called by the orchestration engine when
        transitions complete during loop body chain execution.

        Args:
            transition_id: ID of the completed transition
            result: Result from the completed transition
        """
        # Forward the notification to the loop body chain executor
        if self.loop_body_chain_executor:
            self.loop_body_chain_executor.notify_transition_completion(transition_id, result)

    def get_loop_body_chain_status(self) -> Dict[str, Any]:
        """Get status of active loop body chains for monitoring."""

        if not self.loop_body_chain_executor:
            return {}

        return self.loop_body_chain_executor.get_active_chains_status()

    async def cancel_loop_body_chain(self, chain_id: str, reason: str = "Manual cancellation") -> bool:
        """Cancel an active loop body chain."""

        if not self.loop_body_chain_executor:
            return False

        return await self.loop_body_chain_executor.cancel_chain(chain_id, reason)

    async def monitor_loop_health(self) -> Dict[str, Any]:
        """Monitor the health of the current loop execution."""

        if not hasattr(self, '_current_transition_id') or not self._current_transition_id:
            return {"status": "no_active_loop", "message": "No loop currently executing"}

        # Get chain status from the loop body chain executor
        chain_status = self.loop_body_chain_executor.get_active_chains_status()

        # Get overall loop state
        loop_health = {
            "loop_transition_id": self._current_transition_id,
            "loop_state": {
                "current_iteration_count": len(self.current_iteration_data),
                "completed_iterations": len(self.iteration_results),
                "loop_context": self.loop_context,
                "has_loop_config": self.current_loop_config is not None
            },
            "active_chains": chain_status,
            "chain_count": len(chain_status)
        }

        # Determine overall health status
        if not chain_status:
            loop_health["status"] = "healthy"
            loop_health["message"] = "No active chains"
        else:
            # Check for any unhealthy chains
            unhealthy_chains = []
            for chain_id, status in chain_status.items():
                if status.get("state") in ["timeout", "failed", "cancelled"]:
                    unhealthy_chains.append(chain_id)

            if unhealthy_chains:
                loop_health["status"] = "warning"
                loop_health["message"] = f"Unhealthy chains detected: {unhealthy_chains}"
            else:
                loop_health["status"] = "healthy"
                loop_health["message"] = "All chains healthy"

        return loop_health

    async def cleanup_loop_resources(self) -> None:
        """Clean up resources for the current loop execution."""

        if hasattr(self, '_current_transition_id') and self._current_transition_id:
            # Clean up any stalled chains
            stalled_chains = await self.loop_body_chain_executor.cleanup_stalled_chains()

            if stalled_chains:
                self.logger.warning(
                    f"🧹 Cleaned up {len(stalled_chains)} stalled chains during loop cleanup"
                )

            # Unregister from transition handler
            if self.transition_handler and hasattr(self.transition_handler, 'unregister_loop_executor'):
                self.transition_handler.unregister_loop_executor(self._current_transition_id)
                self.logger.debug(
                    f"🗑️ Unregistered loop executor from transition handler for transition: {self._current_transition_id}"
                )

    def _is_retryable_error(self, error: Exception) -> bool:
        """
        Determine if an error is retryable (transient) or permanent.

        Args:
            error: The exception that occurred

        Returns:
            True if the error is retryable, False otherwise
        """
        # Define retryable error patterns
        retryable_patterns = [
            "timeout",
            "connection",
            "network",
            "temporary",
            "rate limit",
            "service unavailable",
            "502",
            "503",
            "504",
        ]

        error_message = str(error).lower()

        # Check if error message contains retryable patterns
        for pattern in retryable_patterns:
            if pattern in error_message:
                return True

        # Check specific exception types
        retryable_exceptions = [
            ConnectionError,
            TimeoutError,
            asyncio.TimeoutError,
        ]

        for exc_type in retryable_exceptions:
            if isinstance(error, exc_type):
                return True

        # Default to non-retryable for safety
        return False

    async def _execute_with_early_exit_monitoring(
        self, tasks: List[asyncio.Task], cancellation_event: asyncio.Event
    ) -> List[Any]:
        """
        Execute tasks with early exit monitoring.

        Args:
            tasks: List of asyncio tasks to execute
            cancellation_event: Event to signal early exit

        Returns:
            List of task results
        """
        results = []
        pending_tasks = set(tasks)

        while pending_tasks:
            # Wait for at least one task to complete or cancellation
            done, pending = await asyncio.wait(
                pending_tasks,
                return_when=asyncio.FIRST_COMPLETED,
                timeout=1.0,  # Check cancellation every second
            )

            # Process completed tasks
            for task in done:
                try:
                    result = await task
                    results.append(result)
                except Exception as e:
                    self.logger.error(f"Task failed: {str(e)}")
                    results.append((None, None, "failed"))

                pending_tasks.discard(task)

            # Check for early exit
            if cancellation_event.is_set():
                self.logger.info(
                    f"🛑 Cancelling {len(pending_tasks)} remaining tasks due to early exit"
                )
                for task in pending_tasks:
                    task.cancel()

                # Wait for cancellation to complete
                if pending_tasks:
                    await asyncio.wait(pending_tasks, timeout=5.0)
                break

        return results

    async def _check_early_exit_conditions(
        self, result: Any, iteration_index: int
    ) -> bool:
        """
        Check if early exit conditions are met.

        Args:
            result: Result from the completed iteration
            iteration_index: Index of the completed iteration

        Returns:
            True if early exit should be triggered
        """
        # Get early exit configuration
        concurrency_config = self.current_loop_config.get("concurrency", {})
        early_exit_config = concurrency_config.get("early_exit_config", {})

        if not early_exit_config:
            return False

        # Check for success-based early exit
        if early_exit_config.get("on_first_success", False):
            if result and result.get("status") == "completed":
                self.logger.info(
                    f"✅ Early exit triggered: first success at iteration {iteration_index}"
                )
                return True

        # Check for failure threshold
        failure_threshold = early_exit_config.get("failure_threshold")
        if (
            failure_threshold
            and self.concurrent_state["failed_count"] >= failure_threshold
        ):
            self.logger.info(
                f"❌ Early exit triggered: failure threshold ({failure_threshold}) reached"
            )
            return True

        # Check for custom condition
        custom_condition = early_exit_config.get("custom_condition")
        if custom_condition:
            try:
                # Simple condition evaluation (can be enhanced)
                if self._evaluate_custom_condition(
                    custom_condition, result, iteration_index
                ):
                    self.logger.info(
                        f"🎯 Early exit triggered: custom condition met at iteration {iteration_index}"
                    )
                    return True
            except Exception as e:
                self.logger.warning(
                    f"⚠️ Failed to evaluate custom early exit condition: {str(e)}"
                )

        return False

    def _evaluate_custom_condition(
        self, condition: str, result: Any, iteration_index: int
    ) -> bool:
        """
        Evaluate custom early exit condition.

        Args:
            condition: Condition string to evaluate
            result: Result from the iteration
            iteration_index: Index of the iteration

        Returns:
            True if condition is met
        """
        # Simple condition evaluation - can be enhanced with more sophisticated parsing
        # For now, support basic patterns like "result.value > 100"
        try:
            # Create safe evaluation context
            context = {
                "result": result,
                "iteration_index": iteration_index,
                "completed_count": self.concurrent_state["completed_count"],
                "failed_count": self.concurrent_state["failed_count"],
            }

            # Basic safety check - only allow simple expressions
            if any(
                dangerous in condition for dangerous in ["import", "exec", "eval", "__"]
            ):
                self.logger.warning(
                    f"⚠️ Potentially unsafe condition rejected: {condition}"
                )
                return False

            # Simple evaluation (in production, use a proper expression evaluator)
            return eval(condition, {"__builtins__": {}}, context)

        except Exception as e:
            self.logger.warning(
                f"⚠️ Failed to evaluate condition '{condition}': {str(e)}"
            )
            return False

    async def _update_concurrent_progress(self) -> None:
        """Update and log concurrent execution progress."""
        total_iterations = len(self.current_iteration_data)
        completed = self.concurrent_state["completed_count"]
        failed = self.concurrent_state["failed_count"]
        cancelled = self.concurrent_state["cancelled_count"]

        progress_percentage = (completed + failed + cancelled) / total_iterations * 100

        # Log progress every 10% or every 10 iterations
        if (completed + failed + cancelled) % max(1, total_iterations // 10) == 0:
            self.logger.info(
                f"📊 Progress: {progress_percentage:.1f}% "
                f"({completed} completed, {failed} failed, {cancelled} cancelled)"
            )

    async def _ensure_result_ordering(self, results: List[Any]) -> None:
        """
        Ensure results are properly ordered by iteration index.

        Args:
            results: List of results from concurrent execution
        """
        # Results should already be stored in self.iteration_results with proper indexing
        # This method validates and logs any ordering issues

        expected_indices = set(range(len(self.current_iteration_data)))
        actual_indices = set(self.iteration_results.keys())

        missing_indices = expected_indices - actual_indices
        extra_indices = actual_indices - expected_indices

        if missing_indices:
            self.logger.warning(
                f"⚠️ Missing results for iterations: {sorted(missing_indices)}"
            )

        if extra_indices:
            self.logger.warning(
                f"⚠️ Unexpected results for iterations: {sorted(extra_indices)}"
            )

        # Log ordering validation
        ordered_count = len(
            [i for i in sorted(actual_indices) if i in self.iteration_results]
        )
        self.logger.info(
            f"📋 Result ordering validated: {ordered_count}/{len(expected_indices)} results properly indexed"
        )

    async def _log_concurrent_execution_stats(self) -> None:
        """Log detailed statistics about concurrent execution."""
        if not hasattr(self, "concurrent_state"):
            return

        end_time = asyncio.get_event_loop().time()
        execution_time = end_time - self.concurrent_state["start_time"]

        total_iterations = len(self.current_iteration_data)
        completed = self.concurrent_state["completed_count"]
        failed = self.concurrent_state["failed_count"]
        cancelled = self.concurrent_state["cancelled_count"]

        self.logger.info(
            f"🏁 Concurrent execution completed in {execution_time:.2f}s:\n"
            f"   📊 Total iterations: {total_iterations}\n"
            f"   ✅ Completed: {completed} ({completed/total_iterations*100:.1f}%)\n"
            f"   ❌ Failed: {failed} ({failed/total_iterations*100:.1f}%)\n"
            f"   🛑 Cancelled: {cancelled} ({cancelled/total_iterations*100:.1f}%)\n"
            f"   ⚡ Throughput: {total_iterations/execution_time:.2f} iterations/sec"
        )

        if self.concurrent_state.get("early_exit_triggered"):
            self.logger.info("🛑 Execution terminated due to early exit condition")

    async def _adjust_concurrency_dynamically(
        self, semaphore: asyncio.Semaphore, concurrency_config: Dict[str, Any]
    ) -> None:
        """
        Dynamically adjust concurrency based on system resources and performance.

        Args:
            semaphore: Current semaphore controlling concurrency
            concurrency_config: Concurrency configuration
        """
        if not concurrency_config.get("dynamic_adjustment", False):
            return

        try:
            import psutil

            # Get current system metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent

            # Get resource limits
            resource_limits = concurrency_config.get("resource_limits", {})
            cpu_limit = resource_limits.get("cpu_limit_percent", 80)
            memory_limit = resource_limits.get("memory_limit_percent", 80)

            current_concurrent = concurrency_config.get("max_concurrent", 5)

            # Adjust based on resource usage
            if cpu_percent > cpu_limit or memory_percent > memory_limit:
                # Reduce concurrency if resources are strained
                new_concurrent = max(1, current_concurrent - 1)
                self.logger.info(
                    f"🔽 Reducing concurrency: {current_concurrent} → {new_concurrent} "
                    f"(CPU: {cpu_percent:.1f}%, Memory: {memory_percent:.1f}%)"
                )
            elif cpu_percent < cpu_limit * 0.5 and memory_percent < memory_limit * 0.5:
                # Increase concurrency if resources are underutilized
                max_allowed = concurrency_config.get("max_concurrent", 5)
                new_concurrent = min(max_allowed, current_concurrent + 1)
                if new_concurrent > current_concurrent:
                    self.logger.info(
                        f"🔼 Increasing concurrency: {current_concurrent} → {new_concurrent} "
                        f"(CPU: {cpu_percent:.1f}%, Memory: {memory_percent:.1f}%)"
                    )
            else:
                return  # No adjustment needed

            # Update configuration for next adjustment cycle
            concurrency_config["max_concurrent"] = new_concurrent

        except ImportError:
            self.logger.warning(
                "⚠️ psutil not available for dynamic concurrency adjustment"
            )
        except Exception as e:
            self.logger.warning(f"⚠️ Failed to adjust concurrency dynamically: {str(e)}")

    async def _monitor_resource_usage(self, concurrency_config: Dict[str, Any]) -> None:
        """
        Monitor resource usage during concurrent execution.

        Args:
            concurrency_config: Concurrency configuration
        """
        resource_limits = concurrency_config.get("resource_limits", {})
        if not resource_limits:
            return

        try:
            import psutil

            # Get current metrics
            cpu_percent = psutil.cpu_percent()
            memory_info = psutil.virtual_memory()

            # Check limits
            cpu_limit = resource_limits.get("cpu_limit_percent", 100)
            memory_limit_mb = resource_limits.get("memory_limit_mb")

            warnings = []

            if cpu_percent > cpu_limit:
                warnings.append(
                    f"CPU usage ({cpu_percent:.1f}%) exceeds limit ({cpu_limit}%)"
                )

            if memory_limit_mb:
                current_memory_mb = memory_info.used / (1024 * 1024)
                if current_memory_mb > memory_limit_mb:
                    warnings.append(
                        f"Memory usage ({current_memory_mb:.1f}MB) exceeds limit ({memory_limit_mb}MB)"
                    )

            if warnings:
                self.logger.warning(
                    f"⚠️ Resource limits exceeded: {'; '.join(warnings)}"
                )

        except ImportError:
            pass  # psutil not available
        except Exception as e:
            self.logger.debug(f"Resource monitoring failed: {str(e)}")

    async def _evaluate_termination_conditions(
        self, iteration_index: int, iteration_result: Any
    ) -> Dict[str, Any]:
        """
        Evaluate loop termination conditions after each iteration.

        Args:
            iteration_index: Current iteration index
            iteration_result: Result from the current iteration

        Returns:
            Dict containing termination decision and metadata
        """
        termination_config = self.current_loop_config.get("termination_conditions", {})
        if not termination_config:
            return {"should_terminate": False, "reason": None}

        try:
            # Check maximum iterations
            max_iterations = termination_config.get("max_iterations")
            if max_iterations and iteration_index >= max_iterations - 1:
                return {
                    "should_terminate": True,
                    "reason": "max_iterations_reached",
                    "details": f"Reached maximum iterations: {max_iterations}",
                }

            # Check success condition
            success_condition = termination_config.get("success_condition")
            if success_condition and self._evaluate_condition(
                success_condition, iteration_result, iteration_index
            ):
                return {
                    "should_terminate": True,
                    "reason": "success_condition_met",
                    "details": f"Success condition met: {success_condition}",
                }

            # Check failure condition
            failure_condition = termination_config.get("failure_condition")
            if failure_condition and self._evaluate_condition(
                failure_condition, iteration_result, iteration_index
            ):
                return {
                    "should_terminate": True,
                    "reason": "failure_condition_met",
                    "details": f"Failure condition met: {failure_condition}",
                }

            # Check timeout condition
            timeout_seconds = termination_config.get("timeout_seconds")
            if timeout_seconds:
                elapsed_time = asyncio.get_event_loop().time() - self.loop_context.get(
                    "start_time", 0
                )
                if elapsed_time >= timeout_seconds:
                    return {
                        "should_terminate": True,
                        "reason": "timeout_reached",
                        "details": f"Loop timeout reached: {timeout_seconds}s",
                    }

            # Check custom termination condition
            custom_condition = termination_config.get("custom_condition")
            if custom_condition and self._evaluate_condition(
                custom_condition, iteration_result, iteration_index
            ):
                return {
                    "should_terminate": True,
                    "reason": "custom_condition_met",
                    "details": f"Custom condition met: {custom_condition}",
                }

            return {"should_terminate": False, "reason": None}

        except Exception as e:
            self.logger.warning(f"⚠️ Error evaluating termination conditions: {str(e)}")
            return {"should_terminate": False, "reason": None}

    def _evaluate_condition(
        self, condition: str, iteration_result: Any, iteration_index: int
    ) -> bool:
        """
        Safely evaluate a termination condition.

        Args:
            condition: Condition string to evaluate
            iteration_result: Result from the current iteration
            iteration_index: Current iteration index

        Returns:
            True if condition is met, False otherwise
        """
        try:
            # Create safe evaluation context
            context = {
                "result": iteration_result,
                "iteration_index": iteration_index,
                "completed_count": self.loop_context.get("completed_iterations", 0),
                "failed_count": self.loop_context.get("failed_iterations", 0),
                "total_iterations": len(self.current_iteration_data),
                "loop_context": self.loop_context,
            }

            # Add aggregated results if available
            if hasattr(self, "iteration_results") and self.iteration_results:
                context["aggregated_results"] = list(self.iteration_results.values())

            # Safety check - reject dangerous patterns
            dangerous_patterns = [
                "import",
                "exec",
                "eval",
                "__",
                "open",
                "file",
                "input",
                "raw_input",
                "compile",
                "globals",
                "locals",
                "vars",
                "dir",
            ]

            if any(pattern in condition.lower() for pattern in dangerous_patterns):
                self.logger.warning(
                    f"⚠️ Potentially unsafe condition rejected: {condition}"
                )
                return False

            # Evaluate condition with restricted builtins
            safe_builtins = {
                "len": len,
                "str": str,
                "int": int,
                "float": float,
                "bool": bool,
                "min": min,
                "max": max,
                "sum": sum,
                "abs": abs,
                "round": round,
                "any": any,
                "all": all,
                "sorted": sorted,
                "reversed": reversed,
                "enumerate": enumerate,
                "zip": zip,
                "range": range,
                "list": list,
                "dict": dict,
                "set": set,
                "tuple": tuple,
            }

            return bool(eval(condition, {"__builtins__": safe_builtins}, context))

        except Exception as e:
            self.logger.warning(
                f"⚠️ Failed to evaluate condition '{condition}': {str(e)}"
            )
            return False

    async def _handle_loop_break(self, break_reason: str, iteration_index: int) -> None:
        """
        Handle loop break/termination.

        Args:
            break_reason: Reason for breaking the loop
            iteration_index: Index where the break occurred
        """
        self.logger.info(
            f"🛑 Loop break triggered at iteration {iteration_index}: {break_reason}"
        )

        # Update loop context
        self.loop_context["break_reason"] = break_reason
        self.loop_context["break_iteration"] = iteration_index
        self.loop_context["terminated_early"] = True

        # Update loop state manager if available
        if self.loop_state_manager:
            # Store loop context information as metadata
            for key, value in self.loop_context.items():
                self.loop_state_manager.set_loop_metadata(key, value)
            self.loop_state_manager.set_loop_execution_state("completed")

    async def _handle_loop_continue(
        self, continue_reason: str, iteration_index: int
    ) -> None:
        """
        Handle loop continue (skip current iteration).

        Args:
            continue_reason: Reason for continuing/skipping
            iteration_index: Index of the skipped iteration
        """
        self.logger.info(
            f"⏭️ Loop continue triggered at iteration {iteration_index}: {continue_reason}"
        )

        # Update loop context
        if "skipped_iterations" not in self.loop_context:
            self.loop_context["skipped_iterations"] = []
        self.loop_context["skipped_iterations"].append(
            {"index": iteration_index, "reason": continue_reason}
        )

    async def _enable_memory_efficient_iteration(self) -> bool:
        """
        Check if memory-efficient iteration should be enabled.

        Returns:
            True if memory-efficient iteration should be used
        """
        performance_config = self.current_loop_config.get("performance", {})

        # Enable if explicitly configured
        if performance_config.get("memory_efficient", False):
            return True

        # Auto-enable for large datasets
        iteration_count = len(self.current_iteration_data)
        memory_threshold = performance_config.get("memory_threshold", 10000)

        if iteration_count > memory_threshold:
            self.logger.info(
                f"🧠 Enabling memory-efficient iteration for {iteration_count} items (threshold: {memory_threshold})"
            )
            return True

        return False

    async def _create_streaming_iterator(self):
        """
        Create a streaming iterator for large datasets.

        Yields:
            Tuple of (iteration_index, iteration_item)
        """
        performance_config = self.current_loop_config.get("performance", {})
        batch_size = performance_config.get("batch_size", 100)

        self.logger.info(
            f"📊 Creating streaming iterator with batch size: {batch_size}"
        )

        for i in range(0, len(self.current_iteration_data), batch_size):
            batch = self.current_iteration_data[i : i + batch_size]

            # Yield batch items
            for iteration_index, iteration_item in batch:
                yield iteration_index, iteration_item

            # Optional memory cleanup between batches
            if performance_config.get("gc_between_batches", False):
                import gc

                gc.collect()
                self.logger.debug(
                    f"🗑️ Garbage collection completed after batch {i//batch_size + 1}"
                )

    async def _profile_loop_execution(self) -> Dict[str, Any]:
        """
        Profile loop execution performance.

        Returns:
            Dictionary containing performance metrics
        """
        import time
        import tracemalloc

        performance_config = self.current_loop_config.get("performance", {})
        enable_profiling = performance_config.get("enable_profiling", False)

        if not enable_profiling:
            return {}

        self.logger.info("📈 Starting loop execution profiling")

        # Start memory tracing
        tracemalloc.start()
        start_time = time.time()
        start_memory = tracemalloc.get_traced_memory()[0]

        # Store profiling data
        self.profiling_data = {
            "start_time": start_time,
            "start_memory": start_memory,
            "iteration_times": [],
            "memory_snapshots": [],
            "peak_memory": 0,
        }

        return self.profiling_data

    async def _record_iteration_performance(
        self, iteration_index: int, start_time: float, end_time: float
    ) -> None:
        """
        Record performance metrics for a single iteration.

        Args:
            iteration_index: Index of the iteration
            start_time: Start time of iteration
            end_time: End time of iteration
        """
        if not hasattr(self, "profiling_data"):
            return

        iteration_time = end_time - start_time
        self.profiling_data["iteration_times"].append(
            {
                "index": iteration_index,
                "duration": iteration_time,
                "timestamp": end_time,
            }
        )

        # Record memory usage
        try:
            import tracemalloc

            current_memory = tracemalloc.get_traced_memory()[0]
            self.profiling_data["memory_snapshots"].append(
                {
                    "index": iteration_index,
                    "memory": current_memory,
                    "timestamp": end_time,
                }
            )

            # Update peak memory
            if current_memory > self.profiling_data["peak_memory"]:
                self.profiling_data["peak_memory"] = current_memory

        except Exception as e:
            self.logger.debug(f"Memory profiling failed: {str(e)}")

    async def _finalize_performance_profiling(self) -> Dict[str, Any]:
        """
        Finalize performance profiling and generate report.

        Returns:
            Performance profiling report
        """
        if not hasattr(self, "profiling_data"):
            return {}

        import time
        import tracemalloc

        end_time = time.time()
        end_memory = tracemalloc.get_traced_memory()[0]
        tracemalloc.stop()

        # Calculate statistics
        total_time = end_time - self.profiling_data["start_time"]
        memory_delta = end_memory - self.profiling_data["start_memory"]

        iteration_times = [
            item["duration"] for item in self.profiling_data["iteration_times"]
        ]

        report = {
            "total_execution_time": total_time,
            "total_iterations": len(iteration_times),
            "average_iteration_time": (
                sum(iteration_times) / len(iteration_times) if iteration_times else 0
            ),
            "min_iteration_time": min(iteration_times) if iteration_times else 0,
            "max_iteration_time": max(iteration_times) if iteration_times else 0,
            "memory_usage": {
                "start_memory": self.profiling_data["start_memory"],
                "end_memory": end_memory,
                "peak_memory": self.profiling_data["peak_memory"],
                "memory_delta": memory_delta,
            },
            "throughput": len(iteration_times) / total_time if total_time > 0 else 0,
        }

        self.logger.info(
            f"📊 Performance Report:\n"
            f"   ⏱️ Total Time: {total_time:.2f}s\n"
            f"   🔄 Iterations: {len(iteration_times)}\n"
            f"   ⚡ Throughput: {report['throughput']:.2f} iter/sec\n"
            f"   🧠 Peak Memory: {self.profiling_data['peak_memory'] / 1024 / 1024:.2f} MB\n"
            f"   📈 Memory Delta: {memory_delta / 1024 / 1024:.2f} MB"
        )

        return report

    async def _optimize_iteration_execution(self) -> None:
        """
        Apply performance optimizations based on configuration.
        """
        performance_config = self.current_loop_config.get("performance", {})

        # Enable garbage collection optimization
        if performance_config.get("optimize_gc", False):
            import gc

            # Disable automatic garbage collection during loop
            gc.disable()
            self.logger.info("🗑️ Automatic garbage collection disabled for performance")

        # Set thread pool size for concurrent execution
        if performance_config.get("optimize_thread_pool", False):
            import asyncio

            loop = asyncio.get_event_loop()
            thread_pool_size = performance_config.get("thread_pool_size", 4)
            # Note: This is a conceptual optimization - actual implementation would depend on executor setup
            self.logger.info(f"🧵 Thread pool optimized for {thread_pool_size} threads")

    async def _cleanup_performance_optimizations(self) -> None:
        """
        Clean up performance optimizations after loop execution.
        """
        performance_config = self.current_loop_config.get("performance", {})

        # Re-enable garbage collection if it was disabled
        if performance_config.get("optimize_gc", False):
            import gc

            gc.enable()
            gc.collect()  # Force collection to clean up
            self.logger.info("🗑️ Garbage collection re-enabled and cleanup performed")

    def create_iteration_context(
        self, iteration_index: int, iteration_item: Any
    ) -> Dict[str, Any]:
        """
        Create context for a single iteration.

        Args:
            iteration_index: Index of the current iteration
            iteration_item: Data item for this iteration

        Returns:
            Context dictionary for the iteration
        """
        context = {
            "current_item": iteration_item,
            "iteration_index": iteration_index,
            "total_iterations": self.loop_context["total_iterations"],
        }

        # Add previous results for context-preserving loops
        if self.current_loop_config["loop_type"] == "context_preserving":
            context["previous_results"] = [
                self.iteration_results.get(i)
                for i in range(iteration_index)
                if i in self.iteration_results
            ]

        return context

    def aggregate_results(self) -> List[Union[Dict[str, Any], str]]:
        """
        Aggregate results from all iterations using the LoopAggregator.

        Returns:
            Aggregated results based on aggregation configuration
        """
        aggregation_config = self.current_loop_config["aggregation_config"]

        # Use the aggregator for result processing
        aggregated = self.aggregator.aggregate_results(
            self.iteration_results, aggregation_config, preserve_order=True
        )

        # Ensure we return a list for compatibility
        if not isinstance(aggregated, list):
            return [aggregated]
        return aggregated

    async def finalize_loop_execution(self) -> None:
        """
        Finalize loop execution and cleanup.
        """
        # Update loop state manager with final state
        if self.loop_state_manager:
            self.loop_state_manager.set_loop_execution_state("completed")

            # Final state update to workflow state manager
            loop_state_data = self.loop_state_manager.backup_loop_state()
            self.state_manager.store_loop_state(
                self.loop_state_manager.loop_id,
                self.loop_state_manager.transition_id,
                loop_state_data,
            )

            # Save workflow state to persist loop state
            await self.state_manager.save_workflow_state()

        self.logger.info(
            f"Loop execution finalized. "
            f"Completed: {self.loop_context['completed_iterations']}, "
            f"Failed: {self.loop_context['failed_iterations']}"
        )

        # Reset loop state
        self.reset_loop_state()

    def reset_loop_state(self) -> None:
        """
        Reset loop execution state for next execution.
        """
        self.current_loop_config = None
        self.current_iteration_data = []
        self.iteration_results = {}
        self.loop_context = {}
        self.loop_state_manager = None

    async def handle_loop_error(self, error: Exception, transition_id: str) -> None:
        """
        Handle loop-level errors with enhanced error reporting.

        Args:
            error: The exception that occurred
            transition_id: ID of the loop transition
        """
        self.logger.error(f"Loop error in transition {transition_id}: {str(error)}")
        self.logger.error(f"Loop error traceback: {traceback.format_exc()}")

        # Generate comprehensive error report
        error_report = self.error_handler.get_error_report()
        if error_report.get("total_errors", 0) > 0:
            self.logger.info(
                f"📊 Loop Error Summary: {error_report['total_errors']} total errors, "
                f"{error_report['resolved_errors']} resolved "
                f"({error_report['resolution_rate']*100:.1f}% resolution rate)"
            )

            # Log category breakdown
            if error_report.get("category_breakdown"):
                category_info = ", ".join(
                    [
                        f"{cat}: {count}"
                        for cat, count in error_report["category_breakdown"].items()
                    ]
                )
                self.logger.info(f"📋 Error Categories: {category_info}")

        # Update loop state manager if available
        if self.loop_state_manager:
            self.loop_state_manager.set_loop_execution_state("error")
            # Store error information as metadata
            self.loop_state_manager.set_loop_metadata("error", str(error))
            self.loop_state_manager.set_loop_metadata("error_report", error_report)

            # Store updated state
            try:
                loop_state_data = self.loop_state_manager.backup_loop_state()
                self.state_manager.store_loop_state(
                    self.loop_state_manager.loop_id,
                    self.loop_state_manager.transition_id,
                    loop_state_data,
                )
            except Exception as state_error:
                self.logger.warning(
                    f"Failed to store loop error state: {str(state_error)}"
                )

        # Reset state on error
        self.reset_loop_state()

    async def handle_iteration_error(
        self, error: Exception, iteration_index: int, iteration_item: Any
    ) -> None:
        """
        Handle iteration-level errors using enhanced error handler.

        Args:
            error: The exception that occurred
            iteration_index: Index of the failed iteration
            iteration_item: Data item for the failed iteration
        """
        # Create context for error handling
        error_context = {
            "iteration_item": iteration_item,
            "loop_config": self.current_loop_config,
            "loop_context": self.loop_context,
            "max_retries": (
                self.current_loop_config.get("retry_config", {}).get("max_retries", 3)
                if self.current_loop_config
                else 3
            ),
        }

        # Use enhanced error handler
        recovery_action = await self.error_handler.handle_error(
            error, iteration_index, error_context
        )

        self.logger.error(
            f"❌ Iteration {iteration_index} failed: {str(error)} - Action: {recovery_action.get('action', 'unknown')}"
        )

        # Handle recovery action
        if recovery_action.get("action") == "skip":
            # Store skip result
            skip_result = {
                "status": "skipped",
                "error": str(error),
                "recovery_action": recovery_action,
                "iteration_index": iteration_index,
                "iteration_item": iteration_item,
            }
            self.iteration_results[iteration_index] = skip_result
        elif recovery_action.get("action") == "fallback":
            # Use fallback result
            self.iteration_results[iteration_index] = recovery_action.get("result")
        elif recovery_action.get("action") == "abort":
            # Re-raise error to abort loop
            raise error
        else:
            # Default: store error result
            error_result = {
                "iteration_index": iteration_index,
                "iteration_item": iteration_item,
                "status": "failed",
                "error": str(error),
                "recovery_action": recovery_action,
            }
            self.iteration_results[iteration_index] = error_result

        # Update loop state manager if available
        if self.loop_state_manager:
            self.loop_state_manager.set_iteration_status(iteration_index, "failed")
            self.loop_state_manager.store_iteration_result(
                iteration_index, self.iteration_results[iteration_index]
            )

    # ========================================
    # PHASE 4: ORCHESTRATION ENGINE INTEGRATION
    # ========================================

    async def _execute_loop_workflow(self, loop_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the complete 6-step loop workflow:

        3. Payload creation - Create payload requests for loop body based on loop state
        4. Loop body transition execution - Execute entry transitions, let orchestration handle the chain
        5. Result aggregation - Handle all result aggregation and processing
        6. Progression - Move to next iteration or exit when chain completes

        Args:
            loop_state: Initialized loop state from steps 1-2

        Returns:
            Dict containing final loop execution results
        """
        try:
            iteration_data = loop_state.get('iteration_data', [])
            loop_config = loop_state.get('loop_config', {})
            loop_body_config = loop_config.get('loop_body_configuration', {})

            entry_transitions = loop_body_config.get('entry_transitions', [])
            exit_transitions = loop_body_config.get('exit_transitions', [])

            if not entry_transitions:
                return {"error": "No entry transitions defined in loop body configuration", "success": False}

            if not exit_transitions:
                return {"error": "No exit transitions defined in loop body configuration", "success": False}

            # Initialize result collection
            all_iteration_results = []

            # Execute each iteration
            for iteration_index, iteration_item in enumerate(iteration_data):
                self.logger.info(f"🔄 Starting iteration {iteration_index + 1}/{len(iteration_data)}")

                # STEP 3: Payload creation - Create payload for this iteration
                iteration_payload = await self._create_iteration_payload(iteration_item, iteration_index, loop_state)

                # STEP 4: Loop body transition execution - Start entry transitions and wait for exit
                iteration_result = await self._execute_iteration_chain(
                    entry_transitions, exit_transitions, iteration_payload, iteration_index
                )

                if not iteration_result.get('success', False):
                    self.logger.error(f"❌ Iteration {iteration_index + 1} failed: {iteration_result.get('error', 'Unknown error')}")
                    # Handle error based on error handling configuration
                    error_handling = loop_config.get('error_handling', {})
                    if error_handling.get('on_iteration_error', 'continue') == 'exit_loop':
                        break
                    continue

                all_iteration_results.append(iteration_result)
                self.logger.info(f"✅ Iteration {iteration_index + 1} completed successfully")

            # STEP 5: Result aggregation - Aggregate all iteration results
            final_results = await self._aggregate_iteration_results(all_iteration_results, loop_state)

            # STEP 6: Progression - Complete the loop
            return {
                "success": True,
                "final_results": final_results,
                "iteration_count": len(all_iteration_results),
                "total_iterations": len(iteration_data)
            }

        except Exception as e:
            self.logger.error(f"❌ Loop workflow execution failed: {str(e)}")
            return {
                "error": f"Loop workflow execution failed: {str(e)}",
                "success": False
            }

    async def _create_iteration_payload(self, iteration_item: Any, iteration_index: int, loop_state: Dict[str, Any]) -> Any:
        """
        STEP 3: Create payload for this iteration - just the items to pass to entry transition.

        The payload is simply the current iteration item that gets injected into workflow state
        for the entry transition to access through normal handle mapping.

        Args:
            iteration_item: Current item being processed
            iteration_index: Index of current iteration (for context only)
            loop_state: Current loop state (for context only)

        Returns:
            The iteration item that should be passed to the entry transition
        """
        # The payload is simply the current iteration item
        # This is what gets injected into workflow state for the entry transition to access
        return iteration_item

    async def _execute_iteration_chain(
        self,
        entry_transitions: List[str],
        exit_transitions: List[str],
        iteration_payload: Any,
        iteration_index: int
    ) -> Dict[str, Any]:
        """
        STEP 4: Execute loop body transition chain.

        - Inject iteration payload into workflow state
        - Start entry transitions through orchestration engine
        - Wait for exit transitions to complete
        - Let orchestration engine handle the chain automatically

        Args:
            entry_transitions: List of transition IDs to start
            exit_transitions: List of transition IDs to wait for completion
            iteration_payload: The iteration item to pass to entry transitions
            iteration_index: Current iteration index

        Returns:
            Dict containing iteration execution results
        """
        try:
            if not self.orchestration_engine:
                return {"error": "No orchestration engine available for coordination", "success": False}

            self.logger.debug(f"🔄 Executing iteration {iteration_index + 1} chain")

            # Inject iteration payload into workflow state so entry transitions can access it
            await self._inject_iteration_payload_into_state(iteration_payload, iteration_index)

            # Start entry transitions through orchestration engine
            for entry_transition_id in entry_transitions:
                self.logger.debug(f"🚀 Starting entry transition: {entry_transition_id}")
                await self._start_transition_through_orchestration(entry_transition_id)

            # Wait for exit transitions to complete
            self.logger.debug(f"⏳ Waiting for exit transitions: {exit_transitions}")
            results = await self._wait_for_exit_transitions(exit_transitions, iteration_index)

            return {
                "success": True,
                "iteration_index": iteration_index,
                "results": results
            }

        except Exception as e:
            self.logger.error(f"❌ Iteration chain execution failed: {str(e)}")
            return {
                "error": f"Iteration chain execution failed: {str(e)}",
                "success": False,
                "iteration_index": iteration_index
            }

    async def _inject_iteration_payload_into_state(self, iteration_payload: Any, iteration_index: int):
        """
        Inject the iteration payload into workflow state so entry transitions can access it.

        Args:
            iteration_payload: The iteration item to inject
            iteration_index: Current iteration index
        """
        try:
            # Store the current iteration data in a well-known location in workflow state
            # Entry transitions can access this through handle mapping
            iteration_state_key = f"loop_iteration_{iteration_index}"

            if self.state_manager:
                await self.state_manager.set_state_data(iteration_state_key, iteration_payload)
                # Also set a generic "current_iteration" key for easy access
                await self.state_manager.set_state_data("current_iteration", iteration_payload)
                self.logger.debug(f"💾 Injected iteration payload into state: {iteration_state_key}")
            else:
                self.logger.warning("⚠️ No state manager available to inject iteration payload")

        except Exception as e:
            self.logger.error(f"❌ Failed to inject iteration payload: {str(e)}")

    async def _start_transition_through_orchestration(self, transition_id: str):
        """
        Start a transition through the orchestration engine using transition handler methods.

        Args:
            transition_id: ID of the transition to start
        """
        try:
            if not self.orchestration_engine or not hasattr(self.orchestration_engine, 'transition_handler'):
                raise Exception("No orchestration engine or transition handler available")

            transition_handler = self.orchestration_engine.transition_handler

            # Get the transition definition
            transition = self.transitions_by_id.get(transition_id)
            if not transition:
                raise Exception(f"Transition {transition_id} not found")

            # Execute the transition through the transition handler
            # This will trigger the normal orchestration flow
            self.logger.debug(f"🎯 Executing transition {transition_id} through orchestration engine")
            await transition_handler.execute_transition(transition)

        except Exception as e:
            self.logger.error(f"❌ Failed to start transition {transition_id}: {str(e)}")
            raise

    async def _wait_for_exit_transitions(self, exit_transitions: List[str], iteration_index: int) -> Dict[str, Any]:
        """
        Wait for exit transitions to complete and collect their results.

        This method waits for the transition chain to complete by monitoring
        the exit transitions through the transition handler notification system.

        Args:
            exit_transitions: List of transition IDs to wait for
            iteration_index: Current iteration index

        Returns:
            Dict containing results from exit transitions
        """
        try:
            # Set up completion tracking
            completion_tracker = {}
            for exit_transition_id in exit_transitions:
                completion_tracker[exit_transition_id] = {"completed": False, "result": None}

            # Register for transition completion notifications
            # The transition handler will notify us when transitions complete
            if hasattr(self, 'transition_handler') and self.transition_handler:
                for exit_transition_id in exit_transitions:
                    # Register this loop executor to receive notifications for exit transitions
                    if hasattr(self.transition_handler, 'register_loop_executor'):
                        self.transition_handler.register_loop_executor(exit_transition_id, self)

            # Wait for all exit transitions to complete
            max_wait_time = 300  # 5 minutes timeout
            start_time = asyncio.get_event_loop().time()

            while True:
                # Check if all exit transitions are completed
                all_completed = all(
                    completion_tracker[tid]["completed"]
                    for tid in exit_transitions
                )

                if all_completed:
                    break

                # Check timeout
                if asyncio.get_event_loop().time() - start_time > max_wait_time:
                    raise Exception(f"Timeout waiting for exit transitions: {exit_transitions}")

                # Wait a bit before checking again
                await asyncio.sleep(0.1)

            # Collect results from completed exit transitions
            results = {}
            for exit_transition_id in exit_transitions:
                results[exit_transition_id] = completion_tracker[exit_transition_id]["result"]

            self.logger.debug(f"✅ All exit transitions completed for iteration {iteration_index + 1}")
            return results

        except Exception as e:
            self.logger.error(f"❌ Failed to wait for exit transitions: {str(e)}")
            raise

    async def notify_transition_completion(self, transition_id: str, result: Dict[str, Any]):
        """
        Callback method for transition completion notifications.

        This method is called by the transition handler when transitions complete.

        Args:
            transition_id: ID of the completed transition
            result: Result data from the completed transition
        """
        try:
            self.logger.debug(f"🔔 Received completion notification for transition: {transition_id}")

            # Store the result for collection
            if not hasattr(self, '_completion_results'):
                self._completion_results = {}

            self._completion_results[transition_id] = {
                "completed": True,
                "result": result,
                "timestamp": asyncio.get_event_loop().time()
            }

        except Exception as e:
            self.logger.error(f"❌ Failed to handle transition completion notification: {str(e)}")

    async def _aggregate_iteration_results(self, all_iteration_results: List[Dict[str, Any]], loop_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        STEP 5: Aggregate results from all iterations.

        Args:
            all_iteration_results: List of results from each iteration
            loop_state: Current loop state containing aggregation configuration

        Returns:
            Dict containing aggregated final results
        """
        try:
            loop_config = loop_state.get('loop_config', {})
            result_aggregation = loop_config.get('result_aggregation', {})
            strategy = result_aggregation.get('strategy', 'collect_all')

            self.logger.debug(f"🔄 Aggregating {len(all_iteration_results)} iteration results using strategy: {strategy}")

            if strategy == 'collect_all':
                # Simple collection of all results
                return {
                    "strategy": "collect_all",
                    "iteration_results": all_iteration_results,
                    "total_iterations": len(all_iteration_results),
                    "successful_iterations": len([r for r in all_iteration_results if r.get('success', False)])
                }

            elif strategy == 'merge_results':
                # Merge all iteration results into a single result
                merged_data = []
                for iteration_result in all_iteration_results:
                    if iteration_result.get('success', False):
                        results = iteration_result.get('results', {})
                        # Extract actual data from exit transition results
                        for transition_id, transition_result in results.items():
                            if isinstance(transition_result, dict) and 'result' in transition_result:
                                merged_data.append(transition_result['result'])

                return {
                    "strategy": "merge_results",
                    "merged_data": merged_data,
                    "total_iterations": len(all_iteration_results),
                    "successful_iterations": len([r for r in all_iteration_results if r.get('success', False)])
                }

            elif strategy == 'hierarchical_merge':
                # Hierarchical merge based on merge_key
                merge_key = result_aggregation.get('merge_key', 'id')
                hierarchical_results = {}

                for iteration_result in all_iteration_results:
                    if iteration_result.get('success', False):
                        results = iteration_result.get('results', {})
                        for transition_id, transition_result in results.items():
                            if isinstance(transition_result, dict) and 'result' in transition_result:
                                result_data = transition_result['result']
                                if isinstance(result_data, dict) and merge_key in result_data:
                                    key_value = result_data[merge_key]
                                    hierarchical_results[key_value] = result_data

                return {
                    "strategy": "hierarchical_merge",
                    "hierarchical_data": hierarchical_results,
                    "merge_key": merge_key,
                    "total_iterations": len(all_iteration_results),
                    "successful_iterations": len([r for r in all_iteration_results if r.get('success', False)])
                }

            else:
                # Default to collect_all for unknown strategies
                self.logger.warning(f"⚠️ Unknown aggregation strategy: {strategy}, using collect_all")
                return {
                    "strategy": "collect_all",
                    "iteration_results": all_iteration_results,
                    "total_iterations": len(all_iteration_results),
                    "successful_iterations": len([r for r in all_iteration_results if r.get('success', False)])
                }

        except Exception as e:
            self.logger.error(f"❌ Failed to aggregate iteration results: {str(e)}")
            return {
                "error": f"Result aggregation failed: {str(e)}",
                "raw_iteration_results": all_iteration_results
            }

    async def _initialize_loop_state_for_workflow(self, parsed_config: Dict[str, Any], transition_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        STEP 2: Initialize loop state for this specific loop instance.

        Args:
            parsed_config: Parsed loop configuration
            transition_id: ID of the loop transition
            input_data: Input data for the loop

        Returns:
            Dict containing initialized loop state
        """
        try:
            # Extract iteration data from input using handle mappings
            iteration_data = await self._extract_iteration_data_from_input(parsed_config, input_data)
            if not iteration_data:
                self.logger.error("❌ No iteration data found in input")
                return None

            # Initialize loop state
            loop_state = {
                "loop_config": parsed_config,
                "transition_id": transition_id,
                "iteration_data": iteration_data,
                "loop_context": {
                    "start_time": asyncio.get_event_loop().time(),
                    "total_iterations": len(iteration_data),
                    "current_iteration": 0
                },
                "input_data": input_data
            }

            # Initialize loop state manager if needed
            if not self.loop_state_manager:
                from ..loop_state_manager import LoopStateManager
                self.loop_state_manager = LoopStateManager(
                    loop_id=transition_id,
                    state_manager=self.state_manager
                )

            self.logger.info(f"🔄 Initialized loop state for {len(iteration_data)} iterations")
            return loop_state

        except Exception as e:
            self.logger.error(f"❌ Failed to initialize loop state: {str(e)}")
            return None

    async def _extract_iteration_data_from_input(self, parsed_config: Dict[str, Any], input_data: Dict[str, Any]) -> List[Any]:
        """
        Extract iteration data from input using the loop configuration.

        Args:
            parsed_config: Parsed loop configuration
            input_data: Input data for the loop

        Returns:
            List of items to iterate over
        """
        try:
            iteration_source = parsed_config.get('iteration_source', {})

            # Check for static iteration list
            if 'iteration_list' in iteration_source:
                iteration_list = iteration_source['iteration_list']
                if isinstance(iteration_list, list):
                    return iteration_list
                elif isinstance(iteration_list, str):
                    # Handle dynamic reference like "{{prep_data.output.data_items}}"
                    # For now, try to resolve from input_data
                    # This would need proper handle mapping resolution in a full implementation
                    return input_data.get('data_items', [])

            # Check for number range
            if 'number_range' in iteration_source:
                number_range = iteration_source['number_range']
                start = number_range.get('start', 0)
                end = number_range.get('end', 10)
                step = number_range.get('step', 1)
                return list(range(start, end, step))

            # Fallback: try to extract from input_data directly
            if isinstance(input_data, list):
                return input_data
            elif isinstance(input_data, dict):
                # Look for common array field names
                for key in ['items', 'data', 'list', 'array']:
                    if key in input_data and isinstance(input_data[key], list):
                        return input_data[key]

            self.logger.warning("⚠️ No iteration data found, using empty list")
            return []

        except Exception as e:
            self.logger.error(f"❌ Failed to extract iteration data: {str(e)}")
            return []

    def set_orchestration_engine(self, orchestration_engine):
        """Set the orchestration engine for integration."""
        self.orchestration_engine = orchestration_engine
        # Also set it in the chain executor for proper coordination
        if hasattr(self, 'chain_executor') and self.chain_executor:
            self.chain_executor.set_orchestration_engine(orchestration_engine)
        self.logger.debug("🔗 Orchestration engine set for loop executor integration")

    async def _coordinate_with_orchestration_engine(self, transition_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate loop body execution with orchestration engine."""
        if not self.orchestration_engine:
            self.logger.warning("⚠️ No orchestration engine available for coordination")
            return data

        try:
            # Execute transition through orchestration engine
            result = await self.orchestration_engine.execute_transition(transition_id, data)
            self.logger.debug(f"✅ Coordinated transition {transition_id} with orchestration engine")
            return result
        except Exception as e:
            self.logger.error(f"❌ Error coordinating with orchestration engine: {str(e)}")
            raise

    async def _notify_orchestration_engine_completion(self, transition_id: str, result: Dict[str, Any]) -> None:
        """Notify orchestration engine of transition completion."""
        if not self.orchestration_engine:
            return

        try:
            await self.orchestration_engine.handle_transition_completion(transition_id, result)
            self.logger.debug(f"📢 Notified orchestration engine of completion: {transition_id}")
        except Exception as e:
            self.logger.error(f"❌ Error notifying orchestration engine: {str(e)}")

    def _get_workflow_context(self) -> Dict[str, Any]:
        """Retrieve workflow context from orchestration engine."""
        if not self.orchestration_engine:
            return {}

        try:
            context = self.orchestration_engine.get_workflow_context()
            self.logger.debug("📋 Retrieved workflow context from orchestration engine")
            return context
        except Exception as e:
            self.logger.error(f"❌ Error retrieving workflow context: {str(e)}")
            return {}

    def _update_workflow_state(self, state_update: Dict[str, Any]) -> None:
        """Update workflow state through orchestration engine."""
        if not self.orchestration_engine:
            return

        try:
            self.orchestration_engine.update_workflow_state(state_update)
            self.logger.debug("📝 Updated workflow state through orchestration engine")
        except Exception as e:
            self.logger.error(f"❌ Error updating workflow state: {str(e)}")

    async def _execute_loop_body_chain_with_orchestration(
        self, iteration_index: int, iteration_item: Any, iteration_context: Dict[str, Any]
    ) -> Any:
        """Execute loop body chain with orchestration engine coordination."""
        if not self.orchestration_engine:
            # Fallback to direct chain execution
            return await self._execute_loop_body_chain(iteration_index, iteration_item, iteration_context)

        try:
            # Coordinate chain execution with orchestration engine
            chain_data = {
                "iteration_index": iteration_index,
                "iteration_item": iteration_item,
                "iteration_context": iteration_context,
                "loop_config": self.current_loop_config
            }

            # Get loop body configuration
            loop_body_config = self.current_loop_config.get("loop_body_configuration", {})
            entry_transitions = loop_body_config.get("entry_transitions", [])

            if entry_transitions:
                # Execute first transition through orchestration
                first_transition_id = entry_transitions[0]
                result = await self._coordinate_with_orchestration_engine(first_transition_id, chain_data)
                return result
            else:
                # No orchestration needed, execute directly
                return await self._execute_loop_body_chain(iteration_index, iteration_item, iteration_context)

        except Exception as e:
            self.logger.error(f"❌ Error executing loop body chain with orchestration: {str(e)}")
            raise

    async def _synchronize_chain_state_with_orchestration(self, chain_id: str, chain_state: Dict[str, Any]) -> None:
        """Synchronize chain state with orchestration engine."""
        if not self.orchestration_engine:
            return

        try:
            state_update = {
                "chain_id": chain_id,
                "chain_state": chain_state,
                "timestamp": asyncio.get_event_loop().time()
            }
            self._update_workflow_state(state_update)
            self.logger.debug(f"🔄 Synchronized chain state {chain_id} with orchestration engine")
        except Exception as e:
            self.logger.error(f"❌ Error synchronizing chain state: {str(e)}")

    def _emit_loop_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """Emit loop events for orchestration engine integration."""
        if not self.orchestration_engine:
            return

        try:
            event = {
                "event_type": event_type,
                "event_data": event_data,
                "timestamp": asyncio.get_event_loop().time(),
                "loop_transition_id": getattr(self, '_current_transition_id', 'unknown')
            }

            # Update workflow state with event
            self._update_workflow_state({"loop_event": event})
            self.logger.debug(f"📡 Emitted loop event: {event_type}")
        except Exception as e:
            self.logger.error(f"❌ Error emitting loop event: {str(e)}")

    async def _handle_error_with_orchestration(self, error: Exception, context: Dict[str, Any]) -> None:
        """Handle errors with orchestration engine coordination."""
        if not self.orchestration_engine:
            return

        try:
            error_data = {
                "error_type": error.__class__.__name__,
                "error_message": str(error),
                "context": context,
                "timestamp": asyncio.get_event_loop().time()
            }

            # Emit error event
            self._emit_loop_event("loop_error", error_data)
            self.logger.debug("🚨 Handled error with orchestration engine")
        except Exception as e:
            self.logger.error(f"❌ Error handling error with orchestration: {str(e)}")

    async def _coordinate_recovery_with_orchestration(self, error: Exception, recovery_action: str) -> Dict[str, Any]:
        """Coordinate recovery actions with orchestration engine."""
        if not self.orchestration_engine:
            return {"action": "retry", "delay": 1}

        try:
            recovery_data = {
                "error": str(error),
                "recovery_action": recovery_action,
                "timestamp": asyncio.get_event_loop().time()
            }

            # For now, return default recovery action
            # In a full implementation, this would coordinate with orchestration engine
            return {"action": "retry", "delay": 1}
        except Exception as e:
            self.logger.error(f"❌ Error coordinating recovery: {str(e)}")
            return {"action": "retry", "delay": 1}

    async def _persist_loop_state(self, loop_state: Dict[str, Any]) -> None:
        """Persist loop state through orchestration engine."""
        if not self.orchestration_engine:
            return

        try:
            state_update = {
                "loop_state_persistence": loop_state,
                "timestamp": asyncio.get_event_loop().time()
            }
            self._update_workflow_state(state_update)
            self.logger.debug("💾 Persisted loop state through orchestration engine")
        except Exception as e:
            self.logger.error(f"❌ Error persisting loop state: {str(e)}")

    async def _restore_loop_state(self, loop_transition_id: str) -> Dict[str, Any]:
        """Restore loop state from orchestration engine."""
        if not self.orchestration_engine:
            return {}

        try:
            workflow_context = self._get_workflow_context()
            loop_state = workflow_context.get("loop_state", {})
            self.logger.debug(f"🔄 Restored loop state for transition: {loop_transition_id}")
            return loop_state
        except Exception as e:
            self.logger.error(f"❌ Error restoring loop state: {str(e)}")
            return {}

    def _track_performance_metrics(self) -> None:
        """Track performance metrics for orchestration engine integration."""
        try:
            metrics = {
                "execution_time": getattr(self, '_execution_start_time', 0),
                "iterations_completed": len(self.iteration_results),
                "memory_usage": self._get_memory_usage(),
                "timestamp": asyncio.get_event_loop().time()
            }

            # Emit performance metrics event
            self._emit_loop_event("performance_metrics", metrics)
            self.logger.debug("📊 Tracked performance metrics")
        except Exception as e:
            self.logger.error(f"❌ Error tracking performance metrics: {str(e)}")

    async def _report_resource_usage(self, resource_usage: Dict[str, Any]) -> None:
        """Report resource usage to orchestration engine."""
        if not self.orchestration_engine:
            return

        try:
            usage_update = {
                "resource_usage": resource_usage,
                "timestamp": asyncio.get_event_loop().time()
            }
            self._update_workflow_state(usage_update)
            self.logger.debug("📈 Reported resource usage to orchestration engine")
        except Exception as e:
            self.logger.error(f"❌ Error reporting resource usage: {str(e)}")

    def _get_memory_usage(self) -> str:
        """Get current memory usage."""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            return f"{memory_info.rss / 1024 / 1024:.2f}MB"
        except ImportError:
            return "N/A"
        except Exception as e:
            self.logger.error(f"❌ Error getting memory usage: {str(e)}")
            return "N/A"

    async def _finalize_loop_execution(self, final_results: List[Any]) -> None:
        """Finalize loop execution with orchestration engine integration."""
        try:
            # Emit loop completion event
            self._emit_loop_event("loop_completed", {
                "final_results": final_results,
                "total_iterations": len(final_results)
            })

            # Track final performance metrics
            self._track_performance_metrics()

            self.logger.debug("🏁 Finalized loop execution with orchestration integration")
        except Exception as e:
            self.logger.error(f"❌ Error finalizing loop execution: {str(e)}")
