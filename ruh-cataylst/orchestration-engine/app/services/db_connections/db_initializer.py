"""
Database initializer module for centralizing database connections.
This module provides functions to initialize database connections at the server level
rather than creating new connections for each workflow engine instance.
"""

from app.services.db_connections.redis_connections import RedisManager
from app.services.db_connections.postgres_connections import get_postgres_manager
from app.services.db_connections.redis_event_listener import get_redis_event_listener
from app.config.config import settings
from app.utils.enhanced_logger import get_logger

logger = get_logger("DBInitializer")

# Global connection instances
_results_redis_manager = None
_state_redis_manager = None
_postgres_manager = None
_redis_event_listener = None


def initialize_db_connections():
    """
    Initialize all database connections at once.
    This should be called once at server startup.
    
    Returns:
        tuple: (results_redis_manager, state_redis_manager, postgres_manager, redis_event_listener)
    """
    global _results_redis_manager, _state_redis_manager, _postgres_manager, _redis_event_listener
    
    logger.info("Initializing database connections...")
    
    # Initialize Redis for results
    if _results_redis_manager is None:
        try:
            results_db_index = settings.redis_results_db_index
            _results_redis_manager = RedisManager(
                db_index=int(results_db_index) if results_db_index else 0
            )
            logger.info(f"Redis results connection initialized (DB index: {results_db_index})")
        except Exception as e:
            logger.error(f"Error initializing Redis results connection: {e}")
            _results_redis_manager = None
    
    # Initialize Redis for state
    if _state_redis_manager is None:
        try:
            state_db_index = settings.redis_state_db_index
            _state_redis_manager = RedisManager(
                db_index=int(state_db_index) if state_db_index else 1
            )
            logger.info(f"Redis state connection initialized (DB index: {state_db_index})")
        except Exception as e:
            logger.error(f"Error initializing Redis state connection: {e}")
            _state_redis_manager = None
    
    # Initialize PostgreSQL manager
    if _postgres_manager is None:
        try:
            _postgres_manager = get_postgres_manager()
            if _postgres_manager.is_connected():
                logger.info("PostgreSQL connection established")
            else:
                logger.warning("PostgreSQL connection failed")
        except Exception as e:
            logger.error(f"Error initializing PostgreSQL manager: {e}")
            _postgres_manager = None
    
    # Initialize Redis event listener (without workflow state manager initially)
    # Note: The workflow state manager will be set later when WorkflowStateManager instances are created
    if _redis_event_listener is None:
        try:
            _redis_event_listener = get_redis_event_listener()
            logger.info("Redis event listener initialized (workflow state manager will be set later)")
        except Exception as e:
            logger.error(f"Error initializing Redis event listener: {e}")
            _redis_event_listener = None
    
    return _results_redis_manager, _state_redis_manager, _postgres_manager, _redis_event_listener


def get_db_connections():
    """
    Get the current database connections.
    If connections are not initialized, initialize them first.
    
    Returns:
        tuple: (results_redis_manager, state_redis_manager, postgres_manager, redis_event_listener)
    """
    global _results_redis_manager, _state_redis_manager, _postgres_manager, _redis_event_listener
    
    if (_results_redis_manager is None or 
        _state_redis_manager is None or 
        _postgres_manager is None or 
        _redis_event_listener is None):
        return initialize_db_connections()
    
    return _results_redis_manager, _state_redis_manager, _postgres_manager, _redis_event_listener


def close_db_connections():
    """
    Close all database connections.
    This should be called during server shutdown.
    """
    global _results_redis_manager, _state_redis_manager, _postgres_manager, _redis_event_listener
    
    logger.info("Closing database connections...")
    
    if _results_redis_manager:
        try:
            _results_redis_manager.close_connection()
            logger.info("Redis results connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis results connection: {e}")
        _results_redis_manager = None
    
    if _state_redis_manager:
        try:
            _state_redis_manager.close_connection()
            logger.info("Redis state connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis state connection: {e}")
        _state_redis_manager = None
    
    if _postgres_manager:
        try:
            _postgres_manager.close_connection()
            logger.info("PostgreSQL connection closed")
        except Exception as e:
            logger.error(f"Error closing PostgreSQL connection: {e}")
        _postgres_manager = None
    
    if _redis_event_listener:
        try:
            _redis_event_listener.stop()
            logger.info("Redis event listener stopped")
        except Exception as e:
            logger.error(f"Error stopping Redis event listener: {e}")
        _redis_event_listener = None
