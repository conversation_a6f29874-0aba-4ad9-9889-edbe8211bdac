#!/usr/bin/env python3
"""
Test script for the 6-step loop workflow implementation.

This test creates a transition schema with:
1. A loop node with entry and exit transitions
2. Two dummy node executor components in the loop body
3. A third node after the loop
4. Tests the complete workflow execution
"""

import asyncio
import json
import logging
from typing import Dict, Any

# Test transition schema for loop workflow
TEST_TRANSITION_SCHEMA = {
    "workflow_id": "test_loop_workflow",
    "workflow_name": "Test Loop Workflow",
    "description": "Test the 6-step loop workflow implementation",
    "transitions": [
        {
            "transition_id": "start_node",
            "transition_name": "Start Node",
            "transition_type": "node_executor",
            "tool_name": "data_preparation",
            "tool_parameters": {
                "operation": "prepare_test_data"
            },
            "next_transitions": ["loop_node"],
            "input_mapping": {},
            "output_mapping": {}
        },
        {
            "transition_id": "loop_node", 
            "transition_name": "Test Loop Node",
            "transition_type": "loop_executor",
            "tool_name": "loop_processor",
            "tool_parameters": {
                "loop_type": "context_preserving",
                "iteration_source": {
                    "type": "list",
                    "data": [
                        {"user_id": "user1", "action": "process", "data": "test_data_1"},
                        {"user_id": "user2", "action": "validate", "data": "test_data_2"},
                        {"user_id": "user3", "action": "transform", "data": "test_data_3"}
                    ]
                },
                "loop_body_configuration": {
                    "entry_transitions": ["dummy_node_1"],
                    "exit_transitions": ["dummy_node_2"],
                    "chain_completion_detection": "explicit_exit_transitions"
                },
                "result_aggregation": {
                    "strategy": "collect_all"
                },
                "error_handling": {
                    "on_iteration_error": "continue"
                }
            },
            "next_transitions": ["final_node"],
            "input_mapping": {},
            "output_mapping": {}
        },
        {
            "transition_id": "dummy_node_1",
            "transition_name": "Dummy Node 1 - Entry",
            "transition_type": "node_executor", 
            "tool_name": "dummy_processor_1",
            "tool_parameters": {
                "operation": "process_entry",
                "simulate_processing": True
            },
            "next_transitions": ["dummy_node_2"],
            "input_mapping": {
                "current_item": "{{current_iteration}}"
            },
            "output_mapping": {}
        },
        {
            "transition_id": "dummy_node_2",
            "transition_name": "Dummy Node 2 - Exit", 
            "transition_type": "node_executor",
            "tool_name": "dummy_processor_2",
            "tool_parameters": {
                "operation": "process_exit",
                "simulate_processing": True
            },
            "next_transitions": [],
            "input_mapping": {
                "processed_data": "{{dummy_node_1.output}}"
            },
            "output_mapping": {}
        },
        {
            "transition_id": "final_node",
            "transition_name": "Final Node",
            "transition_type": "node_executor",
            "tool_name": "final_processor", 
            "tool_parameters": {
                "operation": "finalize_results"
            },
            "next_transitions": [],
            "input_mapping": {
                "loop_results": "{{loop_node.output}}"
            },
            "output_mapping": {}
        }
    ]
}

# Test input data
TEST_INPUT_DATA = {
    "workflow_id": "test_loop_workflow",
    "initial_data": {
        "test_mode": True,
        "source": "loop_workflow_test"
    }
}

async def setup_logging():
    """Setup logging for the test."""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Set specific loggers
    logging.getLogger('orchestration_engine').setLevel(logging.DEBUG)
    logging.getLogger('loop_executor').setLevel(logging.DEBUG)
    logging.getLogger('transition_handler').setLevel(logging.DEBUG)

async def save_test_schema():
    """Save the test schema to a file for inspection."""
    schema_path = "test_loop_schema.json"
    with open(schema_path, 'w') as f:
        json.dump(TEST_TRANSITION_SCHEMA, f, indent=2)
    print(f"✅ Test schema saved to: {schema_path}")
    return schema_path

async def run_loop_workflow_test():
    """
    Main test function to run the loop workflow.
    
    This will:
    1. Load the orchestration engine
    2. Load the test transition schema
    3. Execute the workflow starting from start_node
    4. Monitor the loop execution through all steps
    5. Verify result aggregation
    """
    try:
        print("🚀 Starting Loop Workflow Test")
        print("=" * 50)
        
        # Setup logging
        await setup_logging()
        
        # Save test schema
        schema_path = await save_test_schema()
        
        # Import orchestration engine components
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

        from app.core_.state_manager import WorkflowStateManager
        from app.core_.transition_handler import TransitionHandler
        from app.services.loop_executor.loop_executor import LoopExecutor
        
        print("📋 Test Schema Overview:")
        print(f"  - Workflow: {TEST_TRANSITION_SCHEMA['workflow_name']}")
        print(f"  - Transitions: {len(TEST_TRANSITION_SCHEMA['transitions'])}")
        print(f"  - Loop iterations: {len(TEST_TRANSITION_SCHEMA['transitions'][1]['tool_parameters']['iteration_source']['data'])}")
        print()
        
        # Initialize state manager
        print("🔧 Initializing State Manager...")
        state_manager = WorkflowStateManager(workflow_id="test_loop_workflow")

        # Create mock components for testing
        from unittest.mock import Mock, AsyncMock

        # Mock workflow utils
        workflow_utils = Mock()

        # Mock result callback
        result_callback = AsyncMock()

        # Create transitions_by_id from schema
        transitions_by_id = {}
        nodes = {}
        for transition in TEST_TRANSITION_SCHEMA['transitions']:
            transitions_by_id[transition['transition_id']] = transition
            # Create a mock node for each transition
            nodes[transition['transition_id']] = {
                "id": transition['transition_id'],
                "type": transition.get('transition_type', 'node_executor')
            }

        # Create transition handler
        print("🔧 Initializing Transition Handler...")
        transition_handler = TransitionHandler(
            state_manager=state_manager,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            dependency_map={},
            workflow_utils=workflow_utils,
            tool_executor=Mock(),
            node_executor=Mock(),
            agent_executor=Mock(),
            result_callback=result_callback,
            user_id="test_user"
        )

        # Create loop executor
        print("🔧 Initializing Loop Executor...")
        loop_executor = LoopExecutor(
            state_manager=state_manager,
            workflow_utils=workflow_utils,
            result_callback=result_callback,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            transition_handler=transition_handler
        )

        # Set orchestration engine reference (mock)
        mock_orchestration_engine = Mock()
        mock_orchestration_engine.transition_handler = transition_handler
        loop_executor.set_orchestration_engine(mock_orchestration_engine)

        # Get the loop transition
        loop_transition = transitions_by_id.get('loop_node')
        if not loop_transition:
            raise Exception("Loop transition not found")

        # Execute the loop transition directly
        print("🎯 Starting loop execution...")
        print("=" * 50)

        # Extract loop parameters
        loop_config = loop_transition.get('tool_parameters', {})

        # Execute the loop
        result = await loop_executor.execute_tool(
            tool_name="loop_processor",
            tool_parameters=loop_config,
            loop_config=loop_config,
            transition_id="loop_node",
            input_data=TEST_INPUT_DATA
        )
        
        print("=" * 50)
        print("🏁 Workflow Execution Complete")
        print(f"📊 Final Result: {json.dumps(result, indent=2)}")
        
        # Check workflow state for loop results
        print("\n🔍 Checking workflow state for loop execution results...")
        loop_result = state_manager.get_transition_result("loop_node")
        if loop_result:
            print(f"📊 Loop execution result: {json.dumps(loop_result, indent=2)}")
        else:
            print("❌ No loop execution result found")

        # Check all loop states
        all_loop_states = state_manager.get_all_loop_states()
        if all_loop_states:
            print(f"📊 All loop states: {json.dumps(all_loop_states, indent=2)}")
        else:
            print("❌ No loop states found")

        print("=" * 50)
        print("✅ Loop workflow test completed successfully!")
        return result
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🧪 Loop Workflow Test Script")
    print("Testing the 6-step loop workflow implementation")
    print()
    
    # Run the test
    result = asyncio.run(run_loop_workflow_test())
    
    if result:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
